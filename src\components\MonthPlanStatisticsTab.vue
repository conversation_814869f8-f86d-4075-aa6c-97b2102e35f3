<template>
  <div class="month-plan-statistics-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-space align="center" :size="16">
        <div class="filter-item">
          <span class="filter-label">单位:</span>
          <n-select
            v-model:value="selectedUnit"
            :options="unitOptions"
            placeholder="全部"
            style="width: 150px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">月份:</span>
          <n-date-picker
            v-model:value="selectedMonth"
            type="month"
            placeholder="2025年全年"
            style="width: 150px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">措施类型:</span>
          <n-select
            v-model:value="selectedMeasureType"
            :options="measureTypeOptions"
            placeholder="全部"
            style="width: 150px"
          />
        </div>

        <n-button type="primary" @click="handleQuery"> 查询 </n-button>

        <n-button type="info" @click="handleExport"> 结果导出 </n-button>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :bordered="true"
        :single-line="false"
        striped
        :scroll-x="1400"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue';
import {
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  NTag,
  type DataTableColumns,
} from 'naive-ui';

// 响应式数据
const selectedUnit = ref(null);
const selectedMonth = ref(null);
const selectedMeasureType = ref(null);

// 下拉选项
const unitOptions = [
  { label: '全部', value: undefined, type: 'option' },
  { label: '川东北气分公司', value: '川东北气分公司', type: 'option' },
  { label: '川西北气矿', value: '川西北气矿', type: 'option' },
  { label: '重庆气矿', value: '重庆气矿', type: 'option' },
  { label: '川中油气矿', value: '川中油气矿', type: 'option' },
  { label: '川东北天然气管理处', value: '川东北天然气管理处', type: 'option' },
  { label: '重庆页岩气公司', value: '重庆页岩气公司', type: 'option' },
  { label: '蜀南气矿', value: '蜀南气矿', type: 'option' },
];

const measureTypeOptions = [
  { label: '全部', value: undefined, type: 'option' },
  { label: '本月度未实施', value: '本月度未实施', type: 'option' },
  { label: '新增', value: '新增', type: 'option' },
  { label: '奇偶', value: '奇偶', type: 'option' },
];

// 获取状态标签类型和颜色
const getStatusStyle = (status: string) => {
  switch (status) {
    case '本月度未实施':
      return { type: 'error' as const, color: '#ff4d4f' };
    case '新增':
      return { type: 'success' as const, color: '#52c41a' };
    case '奇偶':
      return { type: 'warning' as const, color: '#faad14' };
    default:
      return { type: 'default' as const, color: '#d9d9d9' };
  }
};

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_, index) => index + 1,
    fixed: 'left',
  },
  {
    title: '单位',
    key: 'unit',
    width: 120,
    fixed: 'left',
  },
  {
    title: '领域',
    key: 'domain',
    width: 80,
  },
  {
    title: '井号',
    key: 'wellNumber',
    width: 120,
  },
  {
    title: '年度计划安排',
    key: 'yearPlan',
    width: 120,
  },
  {
    title: '计划月度',
    key: 'planMonth',
    width: 100,
  },
  {
    title: '月度预计开口时间',
    key: 'expectedStartTime',
    width: 140,
  },
  {
    title: '措施类型',
    key: 'measureType',
    width: 120,
    render: (row: any) => {
      if (row.measureType) {
        const style = getStatusStyle(row.measureType);
        return h(
          NTag,
          {
            type: style.type,
            size: 'small',
            style: {
              backgroundColor: style.color,
              color: '#fff',
              border: 'none',
            },
          },
          { default: () => row.measureType }
        );
      }
      return '';
    },
  },
  {
    title: '安排类型',
    key: 'implementType',
    width: 120,
    render: (row: any) => {
      if (row.implementType) {
        const style = getStatusStyle(row.implementType);
        return h(
          NTag,
          {
            type: style.type,
            size: 'small',
            style: {
              backgroundColor: style.color,
              color: '#fff',
              border: 'none',
            },
          },
          { default: () => row.implementType }
        );
      }
      return '';
    },
  },
];

// 模拟表格数据
const tableData = ref([
  {
    unit: '',
    domain: '',
    wellNumber: '威203-H1',
    yearPlan: '2025-5',
    planMonth: '2025年5月',
    expectedStartTime: '2025-5-20',
    measureType: '',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威201-H2',
    yearPlan: '2025-5',
    planMonth: '2025年5月',
    expectedStartTime: '',
    measureType: '本月度未实施',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H1',
    yearPlan: '2025-5',
    planMonth: '2025年5月',
    expectedStartTime: '',
    measureType: '本月度未实施',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H2',
    yearPlan: '2025-4',
    planMonth: '2025年4月',
    expectedStartTime: '2025-5-11',
    measureType: '',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H3',
    yearPlan: '',
    planMonth: '2025年4月',
    expectedStartTime: '2025-5-9',
    measureType: '新增',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H4',
    yearPlan: '',
    planMonth: '2025年4月',
    expectedStartTime: '2025-5-18',
    measureType: '新增',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H5',
    yearPlan: '',
    planMonth: '2025年3月',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H6',
    yearPlan: '',
    planMonth: '2025年3月',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威威129H',
    yearPlan: '',
    planMonth: '',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 方法
const handleQuery = () => {
  console.log('查询', {
    unit: selectedUnit.value,
    month: selectedMonth.value,
    measureType: selectedMeasureType.value,
  });
};

const handleExport = () => {
  console.log('结果导出');
};
</script>

<style scoped>
.month-plan-statistics-tab {
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
}

.filter-section {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 60px;
}

.table-section {
  margin-bottom: 20px;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px;
}

/* 自定义标签样式 */
:deep(.n-tag) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .filter-label {
    min-width: auto;
  }
}
</style>
