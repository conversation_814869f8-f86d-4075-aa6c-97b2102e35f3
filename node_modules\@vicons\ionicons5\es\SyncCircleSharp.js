import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M256 48C141.13 48 48 141.13 48 256s93.13 208 208 208s208-93.13 208-208S370.87 48 256 48zm0 320a112.19 112.19 0 0 1-111.27-98.23l-8.86 8.86L113.24 256l46-46l47.55 45.48l-22.12 23.12l-7.2-6.88a80.26 80.26 0 0 0 138.48 37.5l23.77 21.41A112.82 112.82 0 0 1 256 368zm96.79-66l-47.55-45.5l22.12-23.12l6.86 6.55A80.2 80.2 0 0 0 196 202.64l-23.82-21.37A112.18 112.18 0 0 1 367 242.49l9.11-9.12L398.76 256z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'SyncCircleSharp',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
