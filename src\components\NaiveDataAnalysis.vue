<template>
  <div class="naive-data-analysis">
    <!-- 页面头部 -->
    <n-page-header>
      <template #title>数据统计分析</template>
      <template #subtitle>实时监控产量数据，分析生产趋势</template>
      <template #extra>
        <n-space>
          <n-button type="primary" @click="handleRefresh" :loading="loading">
            刷新数据
          </n-button>
          <n-button type="success">
            导出数据
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <!-- 标签页 -->
    <n-tabs v-model:value="activeTab" type="card" size="large" animated>
      <n-tab-pane name="year" tab="年度产量运行分析" />
      <n-tab-pane name="month" tab="月度产量运行分析" />
    </n-tabs>

    <!-- 筛选区域 -->
    <n-card :bordered="false" class="filter-card">
      <template #header>
        <span>筛选条件</span>
      </template>
      
      <n-form
        :model="filterForm"
        label-placement="left"
        label-width="auto"
        inline
      >
        <n-form-item label="数据类型">
          <n-select
            v-model:value="filterForm.type"
            :options="typeOptions"
            placeholder="请选择类型"
            style="width: 150px"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="日期范围">
          <n-date-picker
            v-model:value="filterForm.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 280px"
            clearable
          />
        </n-form-item>
        
        <n-form-item>
          <n-space :size="8">
            <n-button type="primary" @click="handleQuery" :loading="loading">
              查询
            </n-button>
            <n-button @click="handleReset">
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 主要内容区域 -->
    <n-grid :cols="24" :x-gap="16" :y-gap="16">
      <!-- 左侧统计区域 -->
      <n-grid-item :span="6">
        <n-space vertical :size="16">
          <!-- 主要指标卡片 -->
          <n-card class="stat-card plan-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#52c41a">
                📊
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.planOutput)" :precision="2">
                  <template #suffix>亿方</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">计划产量</span>
                  </template>
                </n-statistic>
              </div>
            </n-space>
          </n-card>

          <n-card class="stat-card actual-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#fa8c16">
                📦
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.actualOutput)" :precision="2">
                  <template #suffix>亿方</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">实际产量</span>
                  </template>
                </n-statistic>
              </div>
            </n-space>
          </n-card>

          <n-card class="stat-card completion-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#f5222d">
                ⏱️
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.completionRate)" :precision="2">
                  <template #suffix>%</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">产量完成率</span>
                  </template>
                </n-statistic>
              </div>
            </n-space>
          </n-card>

          <!-- 详细统计卡片 -->
          <n-space vertical :size="12">
            <n-card class="detail-card old-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="success" size="small">旧井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.oldWell.value }}亿方</span>
                  </n-space>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.oldWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="success" size="small">{{ detailStats.oldWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>

            <n-card class="detail-card new-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="info" size="small">上半年新井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.newWell.value }}亿方</span>
                  </n-space>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.newWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="success" size="small">{{ detailStats.newWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>

            <n-card class="detail-card current-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="warning" size="small">当年新井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.currentWell.value }}亿方</span>
                  </n-space>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.currentWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="error" size="small">{{ detailStats.currentWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-space>
        </n-space>
      </n-grid-item>

      <!-- 右侧图表区域 -->
      <n-grid-item :span="18">
        <n-space vertical :size="16">
          <!-- 第一个图表 -->
          <n-card class="chart-card" :bordered="false">
            <template #header>
              <span>2025年计划实际月度产量及累计产量对比图</span>
            </template>
            <div class="chart-container">
              <div ref="monthlyChartRef" class="chart-content"></div>
            </div>
          </n-card>

          <!-- 第二个图表 -->
          <n-card class="chart-card" :bordered="false">
            <template #header>
              <span>2025年月度与年度计划对比曲线图</span>
            </template>
            <div class="chart-container">
              <div ref="trendChartRef" class="chart-content"></div>
            </div>
          </n-card>
        </n-space>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NPageHeader,
  NTabs,
  NTabPane,
  NCard,
  NSpace,
  NForm,
  NFormItem,
  NSelect,
  NDatePicker,
  NButton,
  NGrid,
  NGridItem,
  NAvatar,
  NStatistic,
  NTag,
  NDescriptions,
  NDescriptionsItem,
  useMessage
} from 'naive-ui'
import * as echarts from 'echarts'

const message = useMessage()

// 响应式数据
const activeTab = ref('year')
const loading = ref(false)
const monthlyChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 表单数据
const filterForm = reactive({
  type: null,
  dateRange: null
})

// 选项数据
const typeOptions = [
  { label: '全部类型', value: null },
  { label: '计划产量', value: 'plan' },
  { label: '实际产量', value: 'actual' },
  { label: '累计产量', value: 'cumulative' }
]

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailStats = reactive({
  oldWell: {
    value: '73.89',
    rate: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    rate: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    rate: '98.87',
    excess: '24.31'
  }
})

// 方法
const handleRefresh = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    await initCharts()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = async () => {
  loading.value = true
  try {
    console.log('查询数据', filterForm)
    await new Promise(resolve => setTimeout(resolve, 800))
    await initCharts()
    message.success('查询成功')
  } catch (error) {
    message.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.assign(filterForm, {
    type: null,
    dateRange: null
  })
  handleQuery()
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initMonthlyChart()
  initTrendChart()
}

// 初始化月度产量对比图表
const initMonthlyChart = () => {
  if (!monthlyChartRef.value) return
  
  const chart = echarts.init(monthlyChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: [{
      type: 'category',
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisPointer: { type: 'shadow' }
    }],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: { formatter: '{value}' }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: { formatter: '{value}' }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
        itemStyle: { color: '#fac858' },
        lineStyle: { width: 3 }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
        itemStyle: { color: '#ee6666' },
        lineStyle: { width: 3 }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化趋势对比图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  const chart = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 10,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12']
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)'
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted' }
      }
    ]
  }

  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.naive-data-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.filter-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card {
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card:hover {
  transform: translateY(-2px);
}

.plan-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.completion-card {
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

.detail-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
}

.old-well-card {
  border-left: 4px solid #52c41a;
}

.new-well-card {
  border-left: 4px solid #1890ff;
}

.current-well-card {
  border-left: 4px solid #fa8c16;
}

.chart-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-container {
  height: 350px;
  min-height: 300px;
}

.chart-content {
  width: 100%;
  height: 100%;
}

@media (max-width: 1400px) {
  .naive-data-analysis :deep(.n-grid-item:first-child) {
    grid-column: span 8;
  }

  .naive-data-analysis :deep(.n-grid-item:last-child) {
    grid-column: span 16;
  }
}

@media (max-width: 1200px) {
  .naive-data-analysis :deep(.n-grid-item) {
    grid-column: span 24;
  }

  .chart-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .naive-data-analysis {
    padding: 16px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
