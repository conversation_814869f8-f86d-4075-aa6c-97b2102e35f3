<template>
  <div class="naive-data-analysis">
    <!-- 页面头部 -->
    <n-page-header>
      <template #title>数据统计分析</template>
      <template #subtitle>实时监控产量数据，分析生产趋势</template>
      <template #extra>
        <n-space>
          <n-button type="primary" @click="handleRefresh" :loading="loading">
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            刷新数据
          </n-button>
          <n-dropdown :options="exportOptions" @select="handleExportSelect">
            <n-button type="success">
              <template #icon>
                <n-icon><DownloadIcon /></n-icon>
              </template>
              导出数据
              <template #icon-right>
                <n-icon><ChevronDownIcon /></n-icon>
              </template>
            </n-button>
          </n-dropdown>
        </n-space>
      </template>
    </n-page-header>

    <!-- 标签页 -->
    <n-tabs v-model:value="activeTab" type="card" size="large" animated>
      <n-tab-pane name="year" tab="年度产量运行分析">
        <template #tab>
          <n-space align="center" :size="8">
            <n-icon><CalendarIcon /></n-icon>
            年度产量运行分析
          </n-space>
        </template>
      </n-tab-pane>
      <n-tab-pane name="month" tab="月度产量运行分析">
        <template #tab>
          <n-space align="center" :size="8">
            <n-icon><TrendingUpIcon /></n-icon>
            月度产量运行分析
          </n-space>
        </template>
      </n-tab-pane>
    </n-tabs>

    <!-- 筛选区域 -->
    <n-card :bordered="false" class="filter-card">
      <template #header>
        <n-space align="center" :size="8">
          <n-icon><FilterIcon /></n-icon>
          筛选条件
        </n-space>
      </template>
      
      <n-form
        ref="filterFormRef"
        :model="filterForm"
        label-placement="left"
        label-width="auto"
        inline
      >
        <n-form-item label="数据类型" path="type">
          <n-select
            v-model:value="filterForm.type"
            :options="typeOptions"
            placeholder="请选择类型"
            style="width: 150px"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="日期范围" path="dateRange">
          <n-date-picker
            v-model:value="filterForm.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 280px"
            clearable
          />
        </n-form-item>
        
        <n-form-item label="井类型" path="wellType">
          <n-select
            v-model:value="filterForm.wellType"
            :options="wellTypeOptions"
            placeholder="井类型"
            style="width: 120px"
            clearable
          />
        </n-form-item>
        
        <n-form-item>
          <n-space :size="8">
            <n-button type="primary" @click="handleQuery" :loading="loading">
              <template #icon>
                <n-icon><SearchIcon /></n-icon>
              </template>
              查询
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon><ResetIcon /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 主要内容区域 -->
    <n-grid :cols="24" :x-gap="16" :y-gap="16">
      <!-- 左侧统计区域 -->
      <n-grid-item :span="6">
        <n-space vertical :size="16">
          <!-- 主要指标卡片 -->
          <n-card class="stat-card plan-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#52c41a">
                <n-icon size="24"><TargetIcon /></n-icon>
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.planOutput)" :precision="2">
                  <template #suffix>亿方</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">计划产量</span>
                  </template>
                </n-statistic>
                <n-progress 
                  type="line" 
                  :percentage="85" 
                  :show-indicator="false"
                  :height="4"
                  color="#52c41a"
                  style="margin-top: 8px;"
                />
              </div>
            </n-space>
          </n-card>

          <n-card class="stat-card actual-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#fa8c16">
                <n-icon size="24"><CheckCircleIcon /></n-icon>
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.actualOutput)" :precision="2">
                  <template #suffix>亿方</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">实际产量</span>
                  </template>
                </n-statistic>
                <n-progress 
                  type="line" 
                  :percentage="72" 
                  :show-indicator="false"
                  :height="4"
                  color="#fa8c16"
                  style="margin-top: 8px;"
                />
              </div>
            </n-space>
          </n-card>

          <n-card class="stat-card completion-card" :bordered="false" hoverable>
            <n-space align="center" :size="16">
              <n-avatar size="large" color="#f5222d">
                <n-icon size="24"><PieChartIcon /></n-icon>
              </n-avatar>
              <div>
                <n-statistic :value="parseFloat(statisticsData.completionRate)" :precision="2">
                  <template #suffix>%</template>
                  <template #label>
                    <span style="color: #666; font-size: 14px;">产量完成率</span>
                  </template>
                </n-statistic>
                <n-progress 
                  type="line" 
                  :percentage="parseFloat(statisticsData.completionRate)" 
                  :show-indicator="false"
                  :height="4"
                  color="#f5222d"
                  style="margin-top: 8px;"
                />
              </div>
            </n-space>
          </n-card>

          <!-- 详细统计卡片 -->
          <n-space vertical :size="12">
            <n-card class="detail-card old-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="success" size="small">旧井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.oldWell.value }}亿方</span>
                  </n-space>
                  <n-icon size="16" color="#52c41a"><TrendingUpIcon /></n-icon>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.oldWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="success" size="small">{{ detailStats.oldWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>

            <n-card class="detail-card new-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="info" size="small">上半年新井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.newWell.value }}亿方</span>
                  </n-space>
                  <n-icon size="16" color="#1890ff"><TrendingUpIcon /></n-icon>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.newWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="success" size="small">{{ detailStats.newWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>

            <n-card class="detail-card current-well-card" :bordered="false" size="small" hoverable>
              <template #header>
                <n-space align="center" justify="space-between">
                  <n-space align="center" :size="8">
                    <n-tag type="warning" size="small">当年新井</n-tag>
                    <span style="font-weight: bold;">{{ detailStats.currentWell.value }}亿方</span>
                  </n-space>
                  <n-icon size="16" color="#fa8c16"><TrendingDownIcon /></n-icon>
                </n-space>
              </template>
              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="生产时率">
                  <n-tag type="info" size="small">{{ detailStats.currentWell.rate }}%</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="超欠">
                  <n-tag type="error" size="small">{{ detailStats.currentWell.excess }}亿方</n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </n-card>
          </n-space>
        </n-space>
      </n-grid-item>

      <!-- 右侧图表区域 -->
      <n-grid-item :span="18">
        <n-space vertical :size="16">
          <!-- 第一个图表 -->
          <n-card class="chart-card" :bordered="false">
            <template #header>
              <n-space align="center" justify="space-between">
                <n-space align="center" :size="8">
                  <n-icon size="18" color="#1890ff"><BarChartIcon /></n-icon>
                  <span>2025年计划实际月度产量及累计产量对比图</span>
                </n-space>
                <n-space :size="8">
                  <n-button size="small" quaternary @click="exportMonthlyChart">
                    <template #icon>
                      <n-icon><DownloadIcon /></n-icon>
                    </template>
                  </n-button>
                  <n-button size="small" quaternary @click="refreshMonthlyChart">
                    <template #icon>
                      <n-icon><RefreshIcon /></n-icon>
                    </template>
                  </n-button>
                </n-space>
              </n-space>
            </template>
            <div class="chart-container">
              <div ref="monthlyChartRef" class="chart-content" v-loading="chartLoading"></div>
            </div>
          </n-card>

          <!-- 第二个图表 -->
          <n-card class="chart-card" :bordered="false">
            <template #header>
              <n-space align="center" justify="space-between">
                <n-space align="center" :size="8">
                  <n-icon size="18" color="#1890ff"><LineChartIcon /></n-icon>
                  <span>2025年月度与年度计划对比曲线图</span>
                </n-space>
                <n-space :size="8">
                  <n-button size="small" quaternary @click="exportTrendChart">
                    <template #icon>
                      <n-icon><DownloadIcon /></n-icon>
                    </template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, h } from 'vue'
import {
  NPageHeader,
  NTabs,
  NTabPane,
  NCard,
  NSpace,
  NForm,
  NFormItem,
  NSelect,
  NDatePicker,
  NButton,
  NDropdown,
  NGrid,
  NGridItem,
  NAvatar,
  NIcon,
  NStatistic,
  NProgress,
  NTag,
  NDescriptions,
  NDescriptionsItem,
  useMessage
} from 'naive-ui'
import * as echarts from 'echarts'

// 图标组件 (这里使用简单的 SVG 图标)
const RefreshIcon = () => h('div', '🔄')
const DownloadIcon = () => h('div', '⬇️')
const ChevronDownIcon = () => h('div', '▼')
const CalendarIcon = () => h('div', '📅')
const TrendingUpIcon = () => h('div', '📈')
const FilterIcon = () => h('div', '🔍')
const SearchIcon = () => h('div', '🔍')
const ResetIcon = () => h('div', '🔄')
const TargetIcon = () => h('div', '🎯')
const CheckCircleIcon = () => h('div', '✅')
const PieChartIcon = () => h('div', '📊')
const TrendingDownIcon = () => h('div', '📉')
const BarChartIcon = () => h('div', '📊')
const LineChartIcon = () => h('div', '📈')

const message = useMessage()

// 响应式数据
const activeTab = ref('year')
const loading = ref(false)
const chartLoading = ref(false)
const monthlyChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
const filterFormRef = ref()

// 表单数据
const filterForm = reactive({
  type: null,
  dateRange: null,
  wellType: null
})

// 选项数据
const typeOptions = [
  { label: '全部类型', value: null },
  { label: '计划产量', value: 'plan' },
  { label: '实际产量', value: 'actual' },
  { label: '累计产量', value: 'cumulative' }
]

const wellTypeOptions = [
  { label: '全部井型', value: null },
  { label: '旧井', value: 'old' },
  { label: '新井', value: 'new' },
  { label: '当年新井', value: 'current' }
]

const exportOptions = [
  {
    label: '导出Excel',
    key: 'excel',
    icon: () => h('div', '📊')
  },
  {
    label: '导出PDF',
    key: 'pdf',
    icon: () => h('div', '📄')
  },
  {
    label: '导出图片',
    key: 'image',
    icon: () => h('div', '🖼️')
  }
]

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailStats = reactive({
  oldWell: {
    value: '73.89',
    rate: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    rate: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    rate: '98.87',
    excess: '24.31'
  }
})

// 方法
const handleRefresh = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    await initCharts()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = async () => {
  loading.value = true
  chartLoading.value = true
  try {
    console.log('查询数据', filterForm)
    await new Promise(resolve => setTimeout(resolve, 800))
    await initCharts()
    message.success('查询成功')
  } catch (error) {
    message.error('查询失败')
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

const handleReset = () => {
  Object.assign(filterForm, {
    type: null,
    dateRange: null,
    wellType: null
  })
  handleQuery()
}

const handleExportSelect = (key: string) => {
  switch (key) {
    case 'excel':
      message.success('Excel导出成功')
      break
    case 'pdf':
      message.success('PDF导出成功')
      break
    case 'image':
      exportCharts()
      break
  }
}

const exportCharts = () => {
  exportMonthlyChart()
  exportTrendChart()
}

const exportMonthlyChart = () => {
  // 导出月度图表
  message.success('月度图表导出成功')
}

const exportTrendChart = () => {
  // 导出趋势图表
  message.success('趋势图表导出成功')
}

const refreshMonthlyChart = () => {
  chartLoading.value = true
  setTimeout(() => {
    initMonthlyChart()
    chartLoading.value = false
    message.success('月度图表刷新成功')
  }, 500)
}

const refreshTrendChart = () => {
  chartLoading.value = true
  setTimeout(() => {
    initTrendChart()
    chartLoading.value = false
    message.success('趋势图表刷新成功')
  }, 500)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initMonthlyChart()
  initTrendChart()
}

// 初始化月度产量对比图表
const initMonthlyChart = () => {
  if (!monthlyChartRef.value) return

  const chart = echarts.init(monthlyChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: [{
      type: 'category',
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisPointer: { type: 'shadow' }
    }],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: { formatter: '{value}' }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: { formatter: '{value}' }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
        itemStyle: { color: '#fac858' },
        lineStyle: { width: 3 }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
        itemStyle: { color: '#ee6666' },
        lineStyle: { width: 3 }
      }
    ]
  }

  chart.setOption(option)
}

// 初始化趋势对比图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  const chart = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 10,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12']
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)'
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted' }
      }
    ]
  }

  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.naive-data-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 统计卡片样式 */
.stat-card {
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card:hover {
  transform: translateY(-2px);
}

.plan-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.completion-card {
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

/* 详细统计卡片 */
.detail-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
}

.old-well-card {
  border-left: 4px solid #52c41a;
}

.new-well-card {
  border-left: 4px solid #1890ff;
}

.current-well-card {
  border-left: 4px solid #fa8c16;
}

/* 图表卡片样式 */
.chart-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-container {
  height: 350px;
  min-height: 300px;
}

.chart-content {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .naive-data-analysis :deep(.n-grid-item:first-child) {
    grid-column: span 8;
  }

  .naive-data-analysis :deep(.n-grid-item:last-child) {
    grid-column: span 16;
  }
}

@media (max-width: 1200px) {
  .naive-data-analysis :deep(.n-grid-item) {
    grid-column: span 24;
  }

  .chart-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .naive-data-analysis {
    padding: 16px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
                  </n-button>
                  <n-button size="small" quaternary @click="refreshTrendChart">
                    <template #icon>
                      <n-icon><RefreshIcon /></n-icon>
                    </template>
                  </n-button>
                </n-space>
              </n-space>
            </template>
            <div class="chart-container">
              <div ref="trendChartRef" class="chart-content" v-loading="chartLoading"></div>
            </div>
          </n-card>
        </n-space>
      </n-grid-item>
    </n-grid>
  </div>
</template>
