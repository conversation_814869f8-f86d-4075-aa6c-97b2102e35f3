{"name": "@css-render/plugin-bem", "main": "lib/index.js", "module": "esm/index.js", "license": "MIT", "description": "A plugin of css-render that helping generate BEM standard CSS", "keywords": ["css-render", "css", "style", "css in js", "css-in-js", "bem"], "files": ["lib", "esm"], "author": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/07akioni/css-render", "repository": {"type": "git", "url": "https://github.com/07akioni/css-render"}, "version": "0.15.14", "sideEffects": false, "peerDependencies": {"css-render": "~0.15.14"}, "devDependencies": {"@babel/preset-env": "^7.12.17", "@babel/preset-typescript": "^7.12.17", "@css-render/eslint-config": "0.0.0", "@css-render/test-shared": "0.0.0", "@rushstack/eslint-config": "~2.5.1", "@types/jest": "^27.0.3", "@types/node": "~17.0.5", "babel-jest": "^27.4.5", "css-render": "~0.15.14", "eslint": "~8.5.0", "jest": "^27.4.5", "jest-standard-reporter": "~2.0.0", "typescript": "~4.4.4"}, "scripts": {"lint": "eslint --fix index.ts __tests__/**/*.ts", "test": "jest", "build": "npm run lint && npm run test && rm -rf es lib && tsc -p tsconfig.cjs.json && tsc -p tsconfig.esm.json"}}