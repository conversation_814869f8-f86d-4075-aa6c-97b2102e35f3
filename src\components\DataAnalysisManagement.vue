<template>
  <div class="data-analysis-container">
    <!-- 顶部导航 -->
    <n-tabs v-model:value="activeTab" type="line" size="large">
      <n-tab-pane name="year" tab="年度产量运行分析" />
      <n-tab-pane name="month" tab="月度产量运行分析" />
    </n-tabs>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-space align="center" :size="16">
        <div class="filter-item">
          <span class="filter-label">共类型:</span>
          <n-select
            v-model:value="filterData.type"
            :options="typeOptions"
            placeholder="请选择类型"
            style="width: 150px"
            size="small"
          />
        </div>
        
        <div class="filter-item">
          <span class="filter-label">日期选择:</span>
          <n-date-picker
            v-model:value="filterData.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 250px"
            size="small"
          />
        </div>
        
        <n-button type="primary" size="small" @click="handleQuery">
          查询
        </n-button>
        
        <n-button type="info" size="small" @click="handleExport">
          导出
        </n-button>
      </n-space>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-section">
      <!-- 左侧统计卡片 -->
      <div class="left-panel">
        <div class="stats-cards">
          <!-- 计划产量卡片 -->
          <div class="stat-card plan-card">
            <div class="card-icon">
              <n-icon size="40" color="#4CAF50">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                </svg>
              </n-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ statisticsData.planOutput }}</div>
              <div class="card-unit">亿方</div>
              <div class="card-label">计划产量</div>
            </div>
          </div>

          <!-- 实际产量卡片 -->
          <div class="stat-card actual-card">
            <div class="card-icon">
              <n-icon size="40" color="#FF9800">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"/>
                </svg>
              </n-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ statisticsData.actualOutput }}</div>
              <div class="card-unit">亿方</div>
              <div class="card-label">实际产量</div>
            </div>
          </div>

          <!-- 产量完成率卡片 -->
          <div class="stat-card completion-card">
            <div class="card-icon">
              <n-icon size="40" color="#F44336">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12H12V6Z"/>
                </svg>
              </n-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ statisticsData.completionRate }}</div>
              <div class="card-unit">%</div>
              <div class="card-label">产量完成率</div>
            </div>
          </div>

          <!-- 其他统计卡片 -->
          <div class="additional-stats">
            <div class="stat-row">
              <div class="stat-item old-well">
                <div class="stat-value">{{ statisticsData.oldWell.value }}<span class="unit">亿方</span></div>
                <div class="stat-label">旧井</div>
                <div class="stat-details">
                  <div>实际产量：{{ statisticsData.oldWell.actual }}%</div>
                  <div>超欠：{{ statisticsData.oldWell.excess }}亿方</div>
                </div>
              </div>
            </div>

            <div class="stat-row">
              <div class="stat-item new-well">
                <div class="stat-value">{{ statisticsData.newWell.value }}<span class="unit">亿方</span></div>
                <div class="stat-label">上半年新井</div>
                <div class="stat-details">
                  <div>实际产量：{{ statisticsData.newWell.actual }}%</div>
                  <div>超欠：{{ statisticsData.newWell.excess }}亿方</div>
                </div>
              </div>
            </div>

            <div class="stat-row">
              <div class="stat-item current-well">
                <div class="stat-value">{{ statisticsData.currentWell.value }}<span class="unit">亿方</span></div>
                <div class="stat-label">当年新井</div>
                <div class="stat-details">
                  <div>实际产量：{{ statisticsData.currentWell.actual }}%</div>
                  <div>超欠：{{ statisticsData.currentWell.excess }}亿方</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="right-panel">
        <div class="chart-container">
          <!-- 第一个图表：月度产量对比 -->
          <div class="chart-section">
            <div class="chart-title">
              <n-icon size="16" color="#2196F3">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                </svg>
              </n-icon>
              2025年计划实际月度产量及累计产量对比图
            </div>
            <div class="chart-content" ref="monthlyChart" style="height: 300px;"></div>
          </div>

          <!-- 第二个图表：年度计划对比 -->
          <div class="chart-section">
            <div class="chart-title">
              <n-icon size="16" color="#2196F3">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                </svg>
              </n-icon>
              2025年月度与年度计划对比曲线图
            </div>
            <div class="chart-content" ref="yearlyChart" style="height: 300px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NTabs,
  NTabPane,
  NSpace,
  NSelect,
  NDatePicker,
  NButton,
  NIcon
} from 'naive-ui'
import * as echarts from 'echarts'

// 响应式数据
const activeTab = ref('year')
const monthlyChart = ref<HTMLElement>()
const yearlyChart = ref<HTMLElement>()

// 筛选数据
const filterData = reactive({
  type: null,
  dateRange: null
})

// 下拉选项
const typeOptions = [
  { label: '全部', value: null },
  { label: '计划产量', value: 'plan' },
  { label: '实际产量', value: 'actual' },
  { label: '累计产量', value: 'cumulative' }
]

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22',
  oldWell: {
    value: '73.89',
    actual: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    actual: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    actual: '98.87',
    excess: '24.31'
  }
})

// 图表数据
const chartData = {
  months: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
           '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
  planOutput: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
  actualOutput: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
  cumulativePlan: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
  cumulativeActual: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332]
}

// 方法
const handleQuery = () => {
  console.log('查询', filterData)
  // 重新加载图表数据
  initCharts()
}

const handleExport = () => {
  console.log('导出数据')
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initMonthlyChart()
  initYearlyChart()
}

// 初始化月度产量对比图表
const initMonthlyChart = () => {
  if (!monthlyChart.value) return
  
  const chart = echarts.init(monthlyChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 10
    },
    xAxis: [
      {
        type: 'category',
        data: chartData.months,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: chartData.planOutput,
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: chartData.actualOutput,
        itemStyle: {
          color: '#91cc75'
        }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.cumulativePlan,
        itemStyle: {
          color: '#fac858'
        },
        lineStyle: {
          width: 3
        }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.cumulativeActual,
        itemStyle: {
          color: '#ee6666'
        },
        lineStyle: {
          width: 3
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化年度计划对比图表
const initYearlyChart = () => {
  if (!yearlyChart.value) return

  const chart = echarts.init(yearlyChart.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.months
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)'
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted' }
      }
    ]
  }

  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.data-analysis-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.filter-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.content-section {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.plan-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
}

.completion-card {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.card-icon {
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  display: inline;
}

.card-unit {
  font-size: 16px;
  color: #666;
  margin-left: 4px;
  display: inline;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.additional-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-row {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: left;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-value .unit {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-details {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.old-well {
  border-left: 4px solid #4CAF50;
}

.new-well {
  border-left: 4px solid #2196F3;
}

.current-well {
  border-left: 4px solid #FF9800;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.chart-content {
  flex: 1;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-section {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 100%;
  }

  .stats-cards {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 1;
    min-width: 200px;
  }

  .additional-stats {
    flex-direction: row;
    gap: 16px;
  }

  .stat-row {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }

  .additional-stats {
    flex-direction: column;
  }

  .filter-section .n-space {
    flex-wrap: wrap;
  }
}
</style>
