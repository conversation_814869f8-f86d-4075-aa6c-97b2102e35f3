import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M461 349l-34-19.64a89.53 89.53 0 0 1 20.94-16a22 22 0 0 0-21.28-38.51a133.62 133.62 0 0 0-38.55 32.1L300 256l88.09-50.86a133.46 133.46 0 0 0 38.55 32.1a22 22 0 1 0 21.28-38.51a89.74 89.74 0 0 1-20.94-16l34-19.64A22 22 0 1 0 439 125l-34 19.63a89.74 89.74 0 0 1-3.42-26.15A22 22 0 0 0 380 96h-.41a22 22 0 0 0-22 21.59a133.61 133.61 0 0 0 8.5 49.41L278 217.89V116.18a133.5 133.5 0 0 0 47.07-17.33a22 22 0 0 0-22.71-37.69A89.56 89.56 0 0 1 278 71.27V38a22 22 0 0 0-44 0v33.27a89.56 89.56 0 0 1-24.36-10.11a22 22 0 1 0-22.71 37.69A133.5 133.5 0 0 0 234 116.18v101.71L145.91 167a133.61 133.61 0 0 0 8.52-49.43a22 22 0 0 0-22-21.59H132a22 22 0 0 0-21.59 22.41a89.74 89.74 0 0 1-3.41 26.19L73 125a22 22 0 1 0-22 38.1l34 19.64a89.74 89.74 0 0 1-20.94 16a22 22 0 1 0 21.28 38.51a133.62 133.62 0 0 0 38.55-32.1L212 256l-88.09 50.86a133.62 133.62 0 0 0-38.55-32.1a22 22 0 1 0-21.28 38.51a89.74 89.74 0 0 1 20.94 16L51 349a22 22 0 1 0 22 38.1l34-19.63a89.74 89.74 0 0 1 3.42 26.15A22 22 0 0 0 132 416h.41a22 22 0 0 0 22-21.59a133.61 133.61 0 0 0-8.5-49.41L234 294.11v101.71a133.5 133.5 0 0 0-47.07 17.33a22 22 0 1 0 22.71 37.69A89.56 89.56 0 0 1 234 440.73V474a22 22 0 0 0 44 0v-33.27a89.56 89.56 0 0 1 24.36 10.11a22 22 0 0 0 22.71-37.69A133.5 133.5 0 0 0 278 395.82V294.11L366.09 345a133.61 133.61 0 0 0-8.52 49.43a22 22 0 0 0 22 21.59h.43a22 22 0 0 0 21.59-22.41a89.74 89.74 0 0 1 3.41-26.19l34 19.63A22 22 0 1 0 461 349z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Snow',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
