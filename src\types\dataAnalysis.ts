// 数据分析相关类型定义

export interface FilterData {
  type: string | null
  dateRange: [number, number] | null
}

export interface StatisticsData {
  planOutput: string
  actualOutput: string
  completionRate: string
  oldWell: WellData
  newWell: WellData
  currentWell: WellData
}

export interface WellData {
  value: string
  actual: string
  excess: string
}

export interface ChartData {
  months: string[]
  planOutput: number[]
  actualOutput: number[]
  cumulativePlan: number[]
  cumulativeActual: number[]
}

export interface MonthlyOutputData {
  month: string
  planOutput: number
  actualOutput: number
  cumulativePlan: number
  cumulativeActual: number
}

export interface YearlyComparisonData {
  month: string
  currentWellPlan: number
  currentWellActual: number
  firstHalfWellPlan: number
  firstHalfWellActual: number
  oldWellPlan: number
  oldWellActual: number
}

export interface ExportData {
  type: 'excel' | 'pdf'
  data: any[]
  filename: string
}

export interface SelectOption {
  label: string
  value: string | null
}

// API 响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

export interface DataAnalysisResponse {
  statistics: StatisticsData
  monthlyData: MonthlyOutputData[]
  yearlyData: YearlyComparisonData[]
}
