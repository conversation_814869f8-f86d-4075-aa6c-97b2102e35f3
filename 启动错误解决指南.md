# 启动错误解决指南

## 🚨 常见启动错误及解决方案

### 1. 路径别名 `@` 报错

#### 错误现象
```
Cannot resolve module '@/components/...'
```

#### 解决方案

**步骤 1: 更新 vite.config.ts**
```typescript
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { fileURLToPath, URL } from "node:url";

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
});
```

**步骤 2: 更新 tsconfig.json**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

**步骤 3: 更新 tsconfig.node.json**
```json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "types": ["node"]
  },
  "include": ["vite.config.ts"]
}
```

**步骤 4: 安装 Node.js 类型定义**
```bash
npm install --save-dev @types/node
```

### 2. 脚本命令错误

#### 错误现象
```
npm run serve
# 错误: 'serve' 不是内部或外部命令
```

#### 解决方案
使用正确的脚本命令：
```bash
# 开发环境启动
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 3. 组件导入错误

#### 错误现象
```
Failed to resolve import "@/components/NaiveDataAnalysis.vue"
```

#### 解决方案

**检查文件是否存在**
```bash
# 检查组件文件
ls src/components/NaiveDataAnalysis.vue
```

**检查导入路径**
```typescript
// 正确的导入方式
import NaiveDataAnalysis from '@/components/NaiveDataAnalysis.vue'

// 错误的导入方式
import NaiveDataAnalysis from './components/NaiveDataAnalysis.vue'
```

### 4. Vue 文件语法错误

#### 错误现象
```
Unexpected token '<' in .vue file
```

#### 解决方案

**检查 Vue 文件结构**
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 脚本内容
</script>

<style scoped>
/* 样式内容 */
</style>
```

**常见问题:**
- `<script>` 标签位置错误
- 模板标签未正确闭合
- 缺少必要的导入

### 5. ECharts 相关错误

#### 错误现象
```
Cannot read properties of undefined (reading 'init')
```

#### 解决方案

**确保 ECharts 正确安装**
```bash
npm install echarts
```

**正确的导入方式**
```typescript
import * as echarts from 'echarts'

// 或按需导入
import { init } from 'echarts'
```

### 6. Naive UI 组件错误

#### 错误现象
```
Failed to resolve component: n-button
```

#### 解决方案

**确保 Naive UI 正确安装**
```bash
npm install naive-ui
```

**正确的导入和使用**
```typescript
import { NButton, NCard } from 'naive-ui'

// 在 main.ts 中全局注册
import { createApp } from 'vue'
import naive from 'naive-ui'

const app = createApp(App)
app.use(naive)
```

## 🔧 完整的启动流程

### 1. 检查环境
```bash
# 检查 Node.js 版本 (推荐 16+)
node --version

# 检查 npm 版本
npm --version
```

### 2. 安装依赖
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules
rm -rf node_modules

# 重新安装
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 验证启动
- 浏览器自动打开 `http://localhost:3000`
- 控制台无错误信息
- 页面正常显示

## 🐛 调试技巧

### 1. 查看详细错误信息
```bash
# 启动时显示详细日志
npm run dev --verbose
```

### 2. 检查端口占用
```bash
# Windows
netstat -ano | findstr :3000

# macOS/Linux
lsof -i :3000
```

### 3. 清理缓存
```bash
# 清理 Vite 缓存
rm -rf node_modules/.vite

# 清理 npm 缓存
npm cache clean --force
```

### 4. 重置项目
```bash
# 完全重置
rm -rf node_modules
rm package-lock.json
npm install
npm run dev
```

## ✅ 成功启动标志

当看到以下信息时，表示启动成功：

```
VITE v5.4.19  ready in 723 ms

➜  Local:   http://localhost:3000/
➜  Network: use --host to expose
➜  press h + enter to show help
```

## 📞 获取帮助

如果遇到其他问题：

1. **检查控制台错误信息**
2. **查看浏览器开发者工具**
3. **检查文件路径和导入**
4. **确认依赖版本兼容性**
5. **重启开发服务器**

## 🎯 最佳实践

1. **使用正确的脚本命令**: `npm run dev`
2. **保持依赖版本更新**: 定期运行 `npm update`
3. **正确配置路径别名**: 确保 `@` 指向 `src` 目录
4. **遵循 Vue 3 语法**: 使用 `<script setup>` 语法
5. **按需导入组件**: 减少包体积，提高性能

---

**记住**: 大多数启动错误都是由于配置问题或依赖问题引起的，按照上述步骤逐一排查即可解决！ 🚀
