{"name": "@juggle/resize-observer", "version": "3.4.0", "description": "Polyfills the ResizeObserver API and supports box size options from the latest spec", "main": "lib/exports/resize-observer.umd.js", "module": "lib/exports/resize-observer.js", "types": "lib/exports/resize-observer.d.ts", "files": ["lib/**/*.{js,ts}"], "scripts": {"build": "rm -rf lib && npm run build:esm && npm run build:umd", "build:esm": "tsc", "build:umd": "rollup -c", "build:docs": "rm -f docs/*.* && parcel build docs/src/index.html --out-dir docs --public-url ./", "ci-tests": "npm test -- --ci --runInBand && cat coverage/lcov.info | coveralls", "test": "npm run lint && jest --coverage", "lint": "eslint '{src,test}/**/*.ts'", "preVersion": "npm run build-docs", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+ssh://**************/juggle/resize-observer.git"}, "keywords": ["ResizeObserver", "polyfill", "ponyfill", "event", "resize", "observer", "typescript", "javascript", "element", "component", "container", "queries", "web components", "front-end", "html", "Angular", "React", "<PERSON><PERSON>"], "author": "Juggle", "license": "Apache-2.0", "bugs": {"url": "https://github.com/juggle/resize-observer/issues"}, "homepage": "https://juggle.studio/resize-observer/", "devDependencies": {"@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "core-js": "^3.9.0", "coveralls": "^3.1.0", "cssnano": "^4.1.10", "eslint": "^7.20.0", "jest": "^26.6.3", "jest-cli": "^26.6.3", "jest-junit": "^12.0.0", "jsdom": "^16.4.0", "parcel-bundler": "^1.12.4", "rollup": "^2.39.1", "ts-jest": "^26.5.2", "typescript": "^3.9.9"}}