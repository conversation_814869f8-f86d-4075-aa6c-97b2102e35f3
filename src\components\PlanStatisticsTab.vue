<template>
  <div class="plan-statistics-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-space align="center" :size="16">
        <div class="filter-item">
          <span class="filter-label">时间:</span>
          <n-date-picker
            v-model:value="selectedYear"
            type="year"
            placeholder="2025年"
            style="width: 100px"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">单位:</span>
          <n-select
            v-model:value="selectedUnit"
            :options="unitOptions"
            placeholder="全部"
            style="width: 120px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">措施类型:</span>
          <n-select
            v-model:value="selectedAccuracyType"
            :options="accuracyTypeOptions"
            placeholder="全部"
            style="width: 120px"
          />
        </div>

        <n-button type="primary" @click="handleSearch"> 查询 </n-button>

        <n-button type="primary" @click="handleExport"> 结果导出 </n-button>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :bordered="true"
        :single-line="false"
        striped
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  type DataTableColumns,
} from "naive-ui";

// 响应式数据
const selectedYear = ref(new Date().getFullYear());
const selectedUnit = ref(null);
const selectedAccuracyType = ref(null);

// 下拉选项
const unitOptions = [
  { label: "全部", value: undefined, type: "option" },
  { label: "川中油气矿", value: "川中油气矿", type: "option" },
  { label: "重庆气矿", value: "重庆气矿", type: "option" },
  { label: "川西北气矿", value: "川西北气矿", type: "option" },
];

const accuracyTypeOptions = [
  { label: "全部", value: "initial", type: "option" },
  { label: "高精度", value: "high", type: "option" },
  { label: "中精度", value: "medium", type: "option" },
  { label: "低精度", value: "low", type: "option" },
];

// 表格列定义
const columns: DataTableColumns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    render: (_, index) => index + 1,
  },
  {
    title: "单位",
    key: "unit",
    width: 120,
  },
  {
    title: "领域",
    key: "",
    width: 120,
  },
  {
    title: "井号",
    key: "project",
    width: 120,
  },
  {
    title: "年度计划安排",
    key: "startTime",
    width: 120,
  },
  {
    title: "措施类型",
    key: "accuracyType",
    width: 100,
  },
  {
    title: "措施类型",
    key: "accuracyCount",
    width: 100,
  },
];

// 模拟表格数据
const tableData = ref([
  {
    unit: "川中油气矿",
    project: "蓬莱23-H1",
    startTime: "2025-5",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "川中油气矿",
    project: "蓬莱31-H2",
    startTime: "2025-5",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "川中油气矿",
    project: "蓬莱39-H1",
    startTime: "2025-5",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "重庆气矿",
    project: "蓬莱39-H2",
    startTime: "2025-4",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "重庆气矿",
    project: "蓬莱39-H3",
    startTime: "",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "重庆气矿",
    project: "蓬莱39-H4",
    startTime: "",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "川西北气矿",
    project: "蓬莱39-H5",
    startTime: "",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "川西北气矿",
    project: "蓬莱39-H6",
    startTime: "",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
  {
    unit: "川西北气矿",
    project: "蓬莱129H",
    startTime: "",
    yearPlanCount: "",
    accuracyType: "",
    accuracyCount: "",
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 方法
const handleSearch = () => {
  console.log("执行查询", {
    year: selectedYear.value,
    unit: selectedUnit.value,
    accuracyType: selectedAccuracyType.value,
  });
};

const handleExport = () => {
  console.log("导出结果");
};
</script>

<style scoped>
.plan-statistics-tab {
  width: 100%;
}

.filter-section {
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 60px;
}

.table-section {
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  padding: 16px;
}

.statistics-alert {
  margin-top: 16px;
  padding: 0 16px;
}
</style>
