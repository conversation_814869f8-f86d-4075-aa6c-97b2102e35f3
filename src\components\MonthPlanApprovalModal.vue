<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    :title="modalTitle"
    style="width: 70%; max-width: 800px;"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="approval-modal">
      <!-- 计划信息 -->
      <div class="plan-info">
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item label="计划名称">
            {{ planInfo.planName }}
          </n-descriptions-item>
          <n-descriptions-item label="提交人">
            {{ planInfo.submitter }}
          </n-descriptions-item>
          <n-descriptions-item label="提交时间">
            {{ planInfo.submitTime }}
          </n-descriptions-item>
          <n-descriptions-item label="当前状态">
            <n-tag :type="getStatusType(planInfo.status)" size="small">
              {{ planInfo.status }}
            </n-tag>
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 审批表单 -->
      <div class="approval-form">
        <n-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-placement="left"
          label-width="100px"
          size="medium"
        >
          <n-form-item label="审批结果" path="result">
            <n-radio-group v-model:value="formData.result">
              <n-space>
                <n-radio value="agree">同意</n-radio>
                <n-radio value="disagree">不同意</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>

          <n-form-item label="审批意见" path="opinion">
            <n-input
              v-model:value="formData.opinion"
              type="textarea"
              placeholder="请输入审批意见..."
              :rows="4"
              :maxlength="200"
              show-count
              clearable
            />
          </n-form-item>
        </n-form>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <n-space justify="end">
          <n-button @click="handleClose">
            取消
          </n-button>
          <n-button 
            type="primary" 
            @click="handleConfirm"
            :loading="submitting"
          >
            确认
          </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import {
  NModal,
  NDescriptions,
  NDescriptionsItem,
  NForm,
  NFormItem,
  NRadioGroup,
  NRadio,
  NInput,
  NTag,
  NButton,
  NSpace,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'

// Props
const props = defineProps<{
  show: boolean
  planData?: any
}>()

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const showModal = ref(props.show)
const submitting = ref(false)
const formRef = ref<FormInst | null>(null)
const message = useMessage()

// 监听props变化
watch(() => props.show, (newVal) => {
  showModal.value = newVal
  if (newVal) {
    // 重置表单
    formData.result = ''
    formData.opinion = ''
  }
})

// 监听modal变化
watch(showModal, (newVal) => {
  emits('update:show', newVal)
})

// 计划信息
const planInfo = reactive({
  planName: props.planData?.planName || '川东北气分公司2025年05月常规监测月计划',
  submitter: props.planData?.submitter || '廖华伟',
  submitTime: props.planData?.submitTime || '2025-04-29 09:26:26',
  status: props.planData?.status || '审核中'
})

// 弹框标题
const modalTitle = computed(() => {
  return `审批【${planInfo.planName}】`
})

// 表单数据
const formData = reactive({
  result: '',
  opinion: ''
})

// 表单验证规则
const rules: FormRules = {
  result: [
    {
      required: true,
      message: '请选择审批结果',
      trigger: ['blur', 'change']
    }
  ],
  opinion: [
    {
      required: true,
      message: '请输入审批意见',
      trigger: ['blur', 'input']
    },
    {
      min: 2,
      message: '审批意见至少2个字符',
      trigger: ['blur', 'input']
    }
  ]
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '审核通过':
      return 'success'
    case '审核中':
      return 'warning'
    case '待提交':
      return 'info'
    case '审核不通过':
      return 'error'
    default:
      return 'default'
  }
}

// 方法
const handleClose = () => {
  showModal.value = false
}

const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const approvalData = {
      planId: props.planData?.id,
      planName: planInfo.planName,
      result: formData.result,
      opinion: formData.opinion,
      auditor: '当前用户', // 实际应用中从用户信息获取
      auditTime: new Date().toLocaleString('zh-CN')
    }
    
    emits('confirm', approvalData)
    
    message.success(
      formData.result === 'agree' 
        ? '审批通过，计划已同意' 
        : '审批完成，计划已驳回'
    )
    
    showModal.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听planData变化，更新计划信息
watch(() => props.planData, (newData) => {
  if (newData) {
    planInfo.planName = newData.planName || planInfo.planName
    planInfo.submitter = newData.submitter || planInfo.submitter
    planInfo.submitTime = newData.submitTime || planInfo.submitTime
    planInfo.status = newData.status || planInfo.status
  }
}, { deep: true })
</script>

<style scoped>
.approval-modal {
  padding: 0;
}

.plan-info {
  margin-bottom: 24px;
}

.approval-form {
  margin-bottom: 24px;
}

.footer-buttons {
  padding: 16px 0 0 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
}

/* 描述列表样式 */
:deep(.n-descriptions-item-label) {
  font-weight: 600;
}

/* 表单样式 */
:deep(.n-form-item-label) {
  font-weight: 600;
}

/* 单选按钮样式 */
:deep(.n-radio) {
  margin-right: 16px;
}

/* 文本域样式 */
:deep(.n-input__textarea) {
  resize: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .approval-modal {
    padding: 8px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
}
</style>
