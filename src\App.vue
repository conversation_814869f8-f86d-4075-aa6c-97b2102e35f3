<!--
 * @Description: 
 * @Author: wangfuquan
 * @Date: 2025-05-26 17:28:14
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-06-04 08:45:15
 * @FilePath: \aiTest\src\App.vue
-->
<template>
  <n-config-provider :theme="theme">
    <n-global-style />
    <div id="app">
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <h2>数据分析系统</h2>
        <div class="nav-links">
          <router-link to="/data-analysis-demo" class="nav-link">组件演示</router-link>
          <router-link to="/naive-data-analysis" class="nav-link">Naive UI版</router-link>
          <router-link to="/year-plan" class="nav-link">年度计划</router-link>
          <router-link to="/month-plan" class="nav-link">月度计划</router-link>
          <router-link to="/measure-tracking" class="nav-link">措施跟踪</router-link>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="main-content">
        <router-view />
      </div>
    </div>
  </n-config-provider>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NConfigProvider, NGlobalStyle, lightTheme } from 'naive-ui'

const theme = ref(lightTheme)
</script>

<style>
#app {
  font-family: 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.nav-menu {
  background: #001529;
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-menu h2 {
  margin: 0;
  color: white;
}

.nav-links {
  display: flex;
  gap: 24px;
}

.nav-link {
  color: rgba(255, 255, 255, 0.65);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.main-content {
  min-height: calc(100vh - 80px);
}
</style>
