<!--
 * @Description: 
 * @Author: wangfuquan
 * @Date: 2025-05-26 17:28:14
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-05-28 14:03:40
 * @FilePath: \aiTest\src\App.vue
-->
<template>
  <n-config-provider :theme="theme">
    <n-global-style />
    <div id="app">
      <router-view />
    </div>
  </n-config-provider>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NConfigProvider, NGlobalStyle, lightTheme } from 'naive-ui'

const theme = ref(lightTheme)
</script>

<style>
#app {
  font-family: 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}
</style>
