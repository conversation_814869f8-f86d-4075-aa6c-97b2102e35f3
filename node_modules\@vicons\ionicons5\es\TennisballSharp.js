import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M246.4 480a181 181 0 0 0 3.22-22.86c.35-4.61.53-9.31.53-14c0-100-81.34-181.32-181.32-181.32A181.72 181.72 0 0 0 32 265.61A224.2 224.2 0 0 0 246.4 480z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M284.63 227.37A222.73 222.73 0 0 1 219 68.83a227.09 227.09 0 0 1 2.62-34.42A224.41 224.41 0 0 0 34.41 221.58A227.09 227.09 0 0 1 68.83 219a222.73 222.73 0 0 1 158.54 65.67A222.73 222.73 0 0 1 293 443.17c0 5.74-.22 11.54-.65 17.23s-1.11 11.51-2 17.2a224.42 224.42 0 0 0 187.24-187.18a227.09 227.09 0 0 1-34.42 2.58a222.73 222.73 0 0 1-158.54-65.63z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M443.17 250.15a181.72 181.72 0 0 0 36.83-3.76A224.2 224.2 0 0 0 265.61 32a181.72 181.72 0 0 0-3.76 36.83c0 99.98 81.34 181.32 181.32 181.32z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_5 = [_hoisted_2, _hoisted_3, _hoisted_4]
export default defineComponent({
  name: 'TennisballSharp',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_5)
  }
})
