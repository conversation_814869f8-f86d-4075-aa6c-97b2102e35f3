import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M447.88 313.27l19.25-10.64l-21.28-38.51l-19.25 10.64a133.42 133.42 0 0 0-38.54 32.1L300 256l88.07-50.86a133.42 133.42 0 0 0 38.54 32.1l19.25 10.64l21.28-38.51l-19.25-10.64a89.27 89.27 0 0 1-20.93-16L480 152.05L458 114l-53 30.58a89.07 89.07 0 0 1-3.42-26.15l.41-22l-44-.82l-.41 22a133.62 133.62 0 0 0 8.49 49.39L278 217.89V116.18a133.52 133.52 0 0 0 47.06-17.33L343.9 87.5l-22.71-37.69l-18.84 11.35A89.5 89.5 0 0 1 278 71.27V16h-44v55.27a89.5 89.5 0 0 1-24.35-10.11l-18.84-11.35L168.1 87.5l18.84 11.35A133.52 133.52 0 0 0 234 116.18v101.71L145.93 167a133.62 133.62 0 0 0 8.53-49.43l-.41-22l-44 .82l.41 22a89.07 89.07 0 0 1-3.42 26.15L54 114l-22 38.1l53.05 30.64a89.27 89.27 0 0 1-20.93 16l-19.25 10.63l21.28 38.51l19.25-10.64a133.42 133.42 0 0 0 38.54-32.1L212 256l-88.07 50.86a133.42 133.42 0 0 0-38.54-32.1l-19.24-10.64l-21.28 38.51l19.25 10.64a89.27 89.27 0 0 1 20.93 16L32 360l22 38.1l53.05-30.63a89.07 89.07 0 0 1 3.42 26.15l-.41 22l44 .82l.41-22a133.62 133.62 0 0 0-8.54-49.44L234 294.11v101.71a133.52 133.52 0 0 0-47.06 17.33L168.1 424.5l22.71 37.69l18.84-11.35A89.5 89.5 0 0 1 234 440.73V496h44v-55.27a89.5 89.5 0 0 1 24.35 10.11l18.84 11.35l22.71-37.69l-18.84-11.35A133.52 133.52 0 0 0 278 395.82V294.11L366.07 345a133.62 133.62 0 0 0-8.53 49.43l.41 22l44-.82l-.41-22a89.07 89.07 0 0 1 3.46-26.19l53 30.63L480 360l-53-30.69a89.27 89.27 0 0 1 20.88-16.04z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'SnowSharp',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
