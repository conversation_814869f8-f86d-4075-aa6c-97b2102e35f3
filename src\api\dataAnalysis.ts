import { request } from '@/utils/request'
import type { 
  ApiResponse, 
  DataAnalysisResponse, 
  FilterData,
  ExportData 
} from '@/types/dataAnalysis'

// 获取数据分析统计信息
export const getDataAnalysisStatistics = (params: FilterData): Promise<ApiResponse<DataAnalysisResponse>> => {
  return request({
    url: '/api/data-analysis/statistics',
    method: 'GET',
    params
  })
}

// 获取月度产量数据
export const getMonthlyOutputData = (params: FilterData): Promise<ApiResponse<any>> => {
  return request({
    url: '/api/data-analysis/monthly-output',
    method: 'GET',
    params
  })
}

// 获取年度对比数据
export const getYearlyComparisonData = (params: FilterData): Promise<ApiResponse<any>> => {
  return request({
    url: '/api/data-analysis/yearly-comparison',
    method: 'GET',
    params
  })
}

// 导出数据
export const exportDataAnalysis = (data: ExportData): Promise<Blob> => {
  return request({
    url: '/api/data-analysis/export',
    method: 'POST',
    data,
    responseType: 'blob'
  })
}

// 获取筛选选项
export const getFilterOptions = (): Promise<ApiResponse<any>> => {
  return request({
    url: '/api/data-analysis/filter-options',
    method: 'GET'
  })
}
