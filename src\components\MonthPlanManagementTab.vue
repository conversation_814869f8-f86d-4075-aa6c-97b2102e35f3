<template>
  <div class="month-plan-management-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-space align="center" :size="16">
        <div class="filter-item">
          <span class="filter-label">单位:</span>
          <n-select
            v-model:value="selectedUnit"
            :options="unitOptions"
            placeholder="全部"
            style="width: 150px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">年月:</span>
          <n-date-picker
            v-model:value="selectedMonth"
            type="month"
            placeholder="2025年3月"
            style="width: 150px"
          />
        </div>

        <n-button type="primary" @click="handleQuery"> 查询 </n-button>
      </n-space>

      <!-- 右侧按钮组 -->
      <div class="action-buttons">
        <n-button type="primary" @click="handleAddMonthPlan">
          新增月计划
        </n-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :bordered="true"
        :single-line="false"
        striped
        :scroll-x="1200"
      />
    </div>

    <!-- 新增月计划弹框 -->
    <MonthPlanFormModal
      v-model:show="showMonthPlanModal"
      @save="handleSaveMonthPlan"
      @submit-audit="handleSubmitMonthPlanAudit"
    />

    <!-- 审核记录弹框 -->
    <MonthPlanAuditRecordModal
      v-model:show="showAuditRecordModal"
      :plan-data="selectedPlanData"
    />

    <!-- 审批弹框 -->
    <MonthPlanApprovalModal
      v-model:show="showApprovalModal"
      :plan-data="selectedPlanData"
      @confirm="handleApprovalConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue';
import {
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  NTag,
  type DataTableColumns,
} from 'naive-ui';
import MonthPlanFormModal from './MonthPlanFormModal.vue';
import MonthPlanAuditRecordModal from './MonthPlanAuditRecordModal.vue';
import MonthPlanApprovalModal from './MonthPlanApprovalModal.vue';

// 响应式数据
const selectedUnit = ref(null);
const selectedMonth = ref(new Date().getTime());
const showMonthPlanModal = ref(false);
const showAuditRecordModal = ref(false);
const showApprovalModal = ref(false);
const selectedPlanData = ref(null);

// 下拉选项
const unitOptions = [
  { label: '全部', value: undefined, type: 'option' },
  { label: '川东北气分公司', value: '川东北气分公司', type: 'option' },
  { label: '川西北气矿', value: '川西北气矿', type: 'option' },
  { label: '重庆气矿', value: '重庆气矿', type: 'option' },
  { label: '川中油气矿', value: '川中油气矿', type: 'option' },
  { label: '川东北天然气管理处', value: '川东北天然气管理处', type: 'option' },
  { label: '重庆页岩气公司', value: '重庆页岩气公司', type: 'option' },
  { label: '蜀南气矿', value: '蜀南气矿', type: 'option' },
];

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '审核通过':
      return 'success';
    case '审核中':
      return 'warning';
    case '待提交':
      return 'info';
    default:
      return 'default';
  }
};

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '年月',
    key: 'yearMonth',
    width: 100,
    fixed: 'left',
  },
  {
    title: '油气矿',
    key: 'oilGasField',
    width: 150,
  },
  {
    title: '计划名称',
    key: 'planName',
    width: 300,
    render: (row: any) => {
      return h(
        'a',
        {
          style: {
            color: '#1890ff',
            cursor: 'pointer',
            textDecoration: 'none',
          },
          onClick: () => handleViewPlanDetail(row),
        },
        row.planName
      );
    },
  },
  {
    title: '提交人',
    key: 'submitter',
    width: 80,
  },
  {
    title: '提交时间',
    key: 'submitTime',
    width: 160,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      return h(
        NTag,
        {
          type: getStatusType(row.status),
          size: 'small',
        },
        { default: () => row.status }
      );
    },
  },
  {
    title: '审核记录',
    key: 'auditRecord',
    width: 80,
    render: (row: any) => {
      return h(
        'a',
        {
          style: { color: '#1890ff', cursor: 'pointer' },
          onClick: () => handleViewAuditRecord(row),
        },
        '查看'
      );
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (row: any) => {
      return h(
        'a',
        {
          style: { color: '#1890ff', cursor: 'pointer' },
          onClick: () => handleApproval(row),
        },
        '审批'
      );
    },
  },
];

// 模拟表格数据
const tableData = ref([
  {
    yearMonth: '2025年05月',
    oilGasField: '川东北气分公司',
    planName: '川东北气分公司2025年05月常规监测月计划',
    submitter: '廖华伟',
    submitTime: '2025-04-29 09:26:26',
    status: '审核中',
  },
  {
    yearMonth: '2025年05月',
    oilGasField: '川西北气矿',
    planName: '川西北气矿2025年05月常规监测月计划',
    submitter: '周鑫',
    submitTime: '2025-04-29 09:00:08',
    status: '审核中',
  },
  {
    yearMonth: '2025年05月',
    oilGasField: '重庆气矿',
    planName: '重庆气矿2025年05月常规监测计划',
    submitter: '华青',
    submitTime: '2025-04-29 08:37:36',
    status: '审核中',
  },
  {
    yearMonth: '2025年05月',
    oilGasField: '川中油气矿',
    planName: '川中油气矿2025年05月常规监测计划',
    submitter: '田一军',
    submitTime: '2025-04-25 16:03:26',
    status: '待提交',
  },
  {
    yearMonth: '2025年04月',
    oilGasField: '川东北天然气管理处',
    planName: '川东北天然气管理处2025年04月常规监测月计划',
    submitter: '杨洋',
    submitTime: '2025-04-10 14:13:20',
    status: '审核通过',
  },
  {
    yearMonth: '2025年04月',
    oilGasField: '重庆页岩气公司',
    planName: '重庆页岩气公司2025年04月常规监测月计划',
    submitter: '邓亨建',
    submitTime: '2025-04-08 21:37:46',
    status: '审核通过',
  },
  {
    yearMonth: '2025年03月',
    oilGasField: '蜀南气矿',
    planName: '蜀南气矿2025年03月常规监测计划',
    submitter: '谢志玮',
    submitTime: '2025-04-08 16:01:30',
    status: '审核通过',
  },
  {
    yearMonth: '2025年04月',
    oilGasField: '蜀南气矿',
    planName: '蜀南气矿2025年04月常规监测计划',
    submitter: '谢志玮',
    submitTime: '2025-04-08 16:01:26',
    status: '审核通过',
  },
  {
    yearMonth: '2025年04月',
    oilGasField: '川东北气矿',
    planName: '川东北气矿2025年04月常规监测计划',
    submitter: '谭兴宏',
    submitTime: '2025-04-07 13:37:00',
    status: '审核通过',
  },
  {
    yearMonth: '2025年05月',
    oilGasField: '川东北气矿',
    planName: '川东北气矿2025年05月常规监测计划',
    submitter: '谭兴宏',
    submitTime: '2025-04-07 13:36:53',
    status: '审核通过',
  },
  {
    yearMonth: '2025年04月',
    oilGasField: '川西北气矿',
    planName: '川西北气矿2025年04月常规监测月计划',
    submitter: '周鑫',
    submitTime: '2025-03-31 10:17:57',
    status: '审核通过',
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 方法
const handleQuery = () => {
  console.log('查询', {
    unit: selectedUnit.value,
    month: selectedMonth.value,
  });
};

const handleVersionControl = () => {
  console.log('按钮版权控制');
};

const handleAddMonthPlan = () => {
  console.log('新增月计划');
  showMonthPlanModal.value = true;
};

const handleViewPlanDetail = (row: any) => {
  console.log('查看计划详情:', row);
};

const handleViewAuditRecord = (row: any) => {
  console.log('查看审核记录:', row);
  selectedPlanData.value = row;
  showAuditRecordModal.value = true;
};

const handleApproval = (row: any) => {
  console.log('审批:', row);
  selectedPlanData.value = row;
  showApprovalModal.value = true;
};

const handleSaveMonthPlan = (data: any) => {
  console.log('保存月计划:', data);
  showMonthPlanModal.value = false;
  // 这里可以添加保存逻辑
};

const handleSubmitMonthPlanAudit = (data: any) => {
  console.log('提交月计划审核:', data);
  showMonthPlanModal.value = false;
  // 这里可以添加提交审核逻辑
};

const handleApprovalConfirm = (approvalData: any) => {
  console.log('审批确认:', approvalData);

  // 更新表格中对应行的状态
  const targetIndex = tableData.value.findIndex(
    item => item.planName === approvalData.planName
  );

  if (targetIndex !== -1) {
    tableData.value[targetIndex].status =
      approvalData.result === 'agree' ? '审核通过' : '审核不通过';
  }

  showApprovalModal.value = false;
  // 这里可以添加更新后端数据的逻辑
};
</script>

<style scoped>
.month-plan-management-tab {
  width: 100%;
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 60px;
}

.table-section {
  margin-bottom: 20px;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .filter-label {
    min-width: auto;
  }
}
</style>
