<!--
 * @Description: 措施年度计划管理
 * @Author: wangfuquan
 * @Date: 2025-05-26 17:28:38
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-05-27 09:57:57
 * @FilePath: \kfsc-web\packages\daily-report\src\views\measure-annual-plan\index.vue
-->
<template>
  <div class="year-plan-container">
    <!-- 顶部导航 -->
    <div class="header-nav">
      <n-tabs v-model:value="activeTab" type="line" size="large">
        <n-tab-pane name="management" tab="年计划管理" />
        <n-tab-pane name="statistics" tab="年计划详情" />
      </n-tabs>
    </div>

    <!-- 根据activeTab显示不同的内容 -->
    <div class="tab-content">
      <PlanManagementTab v-if="activeTab === 'management'" />
      <PlanStatisticsTab v-else-if="activeTab === 'statistics'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { NTabs, NTabPane } from 'naive-ui';
import PlanManagementTab from './PlanManagementTab.vue';
import PlanStatisticsTab from './PlanStatisticsTab.vue';

// 响应式数据
const activeTab = ref('management');
</script>

<style scoped>
.year-plan-container {
  padding: 20px;
  background-color: #f5f5f5;
  background-color: #fff;
  min-height: 100vh;
}

.header-nav {
  background-color: #fff;
  padding: 0 16px;
  border-radius: 6px;
}

.tab-content {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .year-plan-container {
    padding: 10px;
  }
}
</style>
