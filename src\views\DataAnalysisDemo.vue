<template>
  <div class="data-analysis-demo">
    <!-- 页面头部 -->
    <div class="demo-header">
      <h1 class="demo-title">数据统计分析模块演示</h1>
      <p class="demo-subtitle">基于图片需求实现的数据分析页面，包含多种展示方式</p>
    </div>

    <!-- 组件选择器 -->
    <div class="component-selector">
      <div class="selector-title">选择组件版本：</div>
      <div class="selector-buttons">
        <button 
          v-for="component in components" 
          :key="component.key"
          :class="['selector-btn', { active: activeComponent === component.key }]"
          @click="activeComponent = component.key"
        >
          <span class="btn-icon">{{ component.icon }}</span>
          <span class="btn-text">{{ component.name }}</span>
          <span class="btn-desc">{{ component.description }}</span>
        </button>
      </div>
    </div>

    <!-- 组件展示区域 -->
    <div class="component-display">
      <div class="display-header">
        <div class="display-title">
          <span class="title-icon">{{ currentComponent.icon }}</span>
          {{ currentComponent.name }}
        </div>
        <div class="display-actions">
          <button class="action-btn" @click="handleFullscreen">
            <span>⛶</span> 全屏查看
          </button>
          <button class="action-btn" @click="handleRefresh">
            <span>🔄</span> 刷新数据
          </button>
        </div>
      </div>
      
      <div class="display-content" ref="displayContentRef">
        <!-- 动态组件 -->
        <component :is="currentComponent.component" />
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="feature-description">
      <div class="desc-title">功能特点</div>
      <div class="desc-content">
        <div class="feature-grid">
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-text">
              <h4>实时数据展示</h4>
              <p>支持实时更新的产量数据统计和分析</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">📈</div>
            <div class="feature-text">
              <h4>多维度图表</h4>
              <p>柱状图、折线图等多种图表类型展示</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <div class="feature-text">
              <h4>灵活筛选</h4>
              <p>支持按类型、日期等多条件筛选数据</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">📤</div>
            <div class="feature-text">
              <h4>数据导出</h4>
              <p>支持Excel、PDF等多种格式导出</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">📱</div>
            <div class="feature-text">
              <h4>响应式设计</h4>
              <p>适配桌面端和移动端多种屏幕尺寸</p>
            </div>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <div class="feature-text">
              <h4>高性能渲染</h4>
              <p>基于ECharts的高性能图表渲染</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术栈说明 -->
    <div class="tech-stack">
      <div class="tech-title">技术栈</div>
      <div class="tech-items">
        <div class="tech-item">
          <span class="tech-name">Vue 3</span>
          <span class="tech-desc">组合式API + TypeScript</span>
        </div>
        <div class="tech-item">
          <span class="tech-name">Naive UI</span>
          <span class="tech-desc">现代化UI组件库</span>
        </div>
        <div class="tech-item">
          <span class="tech-name">ECharts</span>
          <span class="tech-desc">专业图表库</span>
        </div>
        <div class="tech-item">
          <span class="tech-name">TypeScript</span>
          <span class="tech-desc">类型安全</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DataAnalysisManagement from '@/components/DataAnalysisManagement.vue'
import EnhancedDataAnalysis from '@/components/EnhancedDataAnalysis.vue'
import SimpleDataAnalysis from '@/components/SimpleDataAnalysis.vue'

// 响应式数据
const activeComponent = ref('simple')
const displayContentRef = ref<HTMLElement>()

// 组件配置
const components = [
  {
    key: 'simple',
    name: '简化版',
    description: '最接近原图设计',
    icon: '📊',
    component: SimpleDataAnalysis
  },
  {
    key: 'basic',
    name: '基础版',
    description: '功能完整的基础版本',
    icon: '📈',
    component: DataAnalysisManagement
  },
  {
    key: 'enhanced',
    name: '增强版',
    description: '功能丰富的高级版本',
    icon: '🚀',
    component: EnhancedDataAnalysis
  }
]

// 计算属性
const currentComponent = computed(() => {
  return components.find(comp => comp.key === activeComponent.value) || components[0]
})

// 方法
const handleFullscreen = () => {
  if (displayContentRef.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      displayContentRef.value.requestFullscreen()
    }
  }
}

const handleRefresh = () => {
  // 刷新当前组件
  const currentKey = activeComponent.value
  activeComponent.value = ''
  setTimeout(() => {
    activeComponent.value = currentKey
  }, 100)
}
</script>

<style scoped>
.data-analysis-demo {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-title {
  font-size: 32px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.demo-subtitle {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0;
}

.component-selector {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selector-title {
  font-size: 18px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 16px;
}

.selector-buttons {
  display: flex;
  gap: 16px;
}

.selector-btn {
  flex: 1;
  padding: 20px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.selector-btn:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
}

.selector-btn.active {
  border-color: #1890ff;
  background: #f6ffed;
}

.btn-icon {
  font-size: 24px;
  display: block;
  margin-bottom: 8px;
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  display: block;
  margin-bottom: 4px;
}

.btn-desc {
  font-size: 14px;
  color: #8c8c8c;
  display: block;
}

.component-display {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.display-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
  color: #262626;
}

.title-icon {
  font-size: 20px;
}

.display-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #595959;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.display-content {
  background: #f5f5f5;
}

.feature-description {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.desc-title {
  font-size: 20px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 20px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.feature-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.feature-text h4 {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 4px 0;
}

.feature-text p {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.5;
}

.tech-stack {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tech-title {
  font-size: 20px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 20px;
}

.tech-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  min-width: 120px;
}

.tech-name {
  font-size: 16px;
  font-weight: 500;
  color: #389e0d;
  margin-bottom: 4px;
}

.tech-desc {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-analysis-demo {
    padding: 16px;
  }
  
  .selector-buttons {
    flex-direction: column;
  }
  
  .display-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .tech-items {
    justify-content: center;
  }
}
</style>
