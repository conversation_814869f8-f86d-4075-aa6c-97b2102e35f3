import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M200.66 352H144a96 96 0 0 1 0-192h55.41',
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-linejoin': 'round',
    'stroke-width': '48'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M312.59 160H368a96 96 0 0 1 0 192h-56.66',
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-linejoin': 'round',
    'stroke-width': '48'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = [_hoisted_2, _hoisted_3]
export default defineComponent({
  name: 'UnlinkSharp',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_4)
  }
})
