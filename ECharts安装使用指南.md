# ECharts 安装使用指南

## 📦 安装 ECharts

### 1. 使用 npm 安装

```bash
npm install echarts
```

### 2. 使用 yarn 安装

```bash
yarn add echarts
```

### 3. 使用 pnpm 安装

```bash
pnpm add echarts
```

## 🔧 项目配置

### 1. TypeScript 类型支持

ECharts 5.x 版本已经内置了 TypeScript 类型定义，无需额外安装类型包。

### 2. 基础导入方式

```typescript
// 完整导入
import * as echarts from 'echarts'

// 按需导入（推荐，减小包体积）
import { init, registerTheme } from 'echarts'
import { BarChart, LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  BarChart,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
])
```

## 🛠️ 项目中的 ECharts 配置

### 1. 工具函数 (`src/utils/echarts.ts`)

提供了以下功能：
- ✅ 自定义主题配置
- ✅ 通用图表配置
- ✅ 图表实例管理
- ✅ 柱状图配置生成器
- ✅ 折线图配置生成器
- ✅ 混合图表配置生成器
- ✅ 图表导出功能

### 2. 组合式函数 (`src/composables/useECharts.ts`)

提供了以下功能：
- ✅ 图表生命周期管理
- ✅ 响应式图表实例
- ✅ 加载状态管理
- ✅ 错误处理
- ✅ 自动调整大小
- ✅ 图表导出

### 3. 专用数据分析图表函数

```typescript
import { useDataAnalysisCharts } from '@/composables/useECharts'

const {
  monthlyChartRef,
  trendChartRef,
  monthlyChart,
  trendChart,
  updateMonthlyChart,
  updateTrendChart
} = useDataAnalysisCharts()
```

## 📊 使用示例

### 1. 基础使用

```vue
<template>
  <div>
    <div ref="chartRef" style="width: 100%; height: 400px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useECharts } from '@/composables/useECharts'

const chartRef = ref<HTMLElement>()
const chart = useECharts(chartRef)

onMounted(() => {
  const option = {
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'bar'
    }]
  }
  
  chart.setOption(option)
})
</script>
```

### 2. 数据分析图表使用

```vue
<template>
  <div class="charts-container">
    <div ref="monthlyChartRef" class="chart"></div>
    <div ref="trendChartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useDataAnalysisCharts } from '@/composables/useECharts'

const {
  monthlyChartRef,
  trendChartRef,
  updateMonthlyChart,
  updateTrendChart
} = useDataAnalysisCharts()

onMounted(() => {
  // 更新月度图表
  updateMonthlyChart({
    months: ['2025-01', '2025-02', '2025-03'],
    planOutput: [35, 32, 28],
    actualOutput: [33, 30, 25],
    cumulativePlan: [35, 67, 95],
    cumulativeActual: [33, 63, 88]
  })

  // 更新趋势图表
  updateTrendChart({
    months: ['2025-01', '2025-02', '2025-03'],
    currentWellPlan: [8, 7.5, 7],
    currentWellActual: [7.8, 7.2, 6.8],
    firstHalfWellPlan: [6, 5.8, 5.6],
    firstHalfWellActual: [5.8, 5.6, 5.4],
    oldWellPlan: [21, 19, 15.4],
    oldWellActual: [19.4, 17.4, 12.8]
  })
})
</script>

<style scoped>
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>
```

## 🎨 主题配置

### 1. 自定义主题

```typescript
import { registerTheme } from 'echarts'

const customTheme = {
  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666'],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'Arial, sans-serif',
    fontSize: 12,
    color: '#333'
  }
}

registerTheme('custom', customTheme)
```

### 2. 使用主题

```typescript
const chart = echarts.init(container, 'custom')
```

## 📈 图表类型

### 1. 柱状图

```typescript
import { createBarChartOption } from '@/utils/echarts'

const option = createBarChartOption({
  categories: ['Jan', 'Feb', 'Mar'],
  series: [
    {
      name: '销量',
      data: [120, 200, 150],
      color: '#5470c6'
    }
  ],
  title: '月度销量统计',
  yAxisName: '销量(件)'
})
```

### 2. 折线图

```typescript
import { createLineChartOption } from '@/utils/echarts'

const option = createLineChartOption({
  categories: ['Jan', 'Feb', 'Mar'],
  series: [
    {
      name: '趋势',
      data: [120, 200, 150],
      color: '#91cc75',
      lineStyle: 'solid'
    }
  ],
  title: '趋势分析',
  yAxisName: '数值'
})
```

### 3. 混合图表

```typescript
import { createMixedChartOption } from '@/utils/echarts'

const option = createMixedChartOption({
  categories: ['Jan', 'Feb', 'Mar'],
  barSeries: [
    {
      name: '柱状数据',
      data: [120, 200, 150],
      color: '#5470c6'
    }
  ],
  lineSeries: [
    {
      name: '折线数据',
      data: [80, 120, 100],
      color: '#91cc75',
      yAxisIndex: 1
    }
  ],
  yAxis: [
    { name: '柱状轴', position: 'left' },
    { name: '折线轴', position: 'right' }
  ]
})
```

## 🔧 高级功能

### 1. 图表导出

```typescript
// 导出为图片
chart.exportImage('图表名称', {
  type: 'png',
  backgroundColor: '#fff'
})

// 获取数据URL
const dataURL = chart.getDataURL({
  type: 'png',
  pixelRatio: 2
})
```

### 2. 加载状态

```typescript
// 显示加载
chart.showLoading('加载中...')

// 隐藏加载
chart.hideLoading()
```

### 3. 响应式调整

```typescript
// 手动调整大小
chart.resize()

// 自动调整（已在 useECharts 中实现）
window.addEventListener('resize', () => {
  chart.resize()
})
```

## 🐛 常见问题

### 1. 图表不显示

**原因**: 容器元素没有设置高度
**解决**: 确保容器元素有明确的高度

```css
.chart-container {
  width: 100%;
  height: 400px; /* 必须设置高度 */
}
```

### 2. 图表模糊

**原因**: 高分辨率屏幕下像素比问题
**解决**: 设置 devicePixelRatio

```typescript
const chart = echarts.init(container, theme, {
  devicePixelRatio: window.devicePixelRatio
})
```

### 3. 内存泄漏

**原因**: 图表实例没有正确销毁
**解决**: 在组件卸载时销毁图表

```typescript
onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
})
```

## 📚 参考资源

- [ECharts 官方文档](https://echarts.apache.org/zh/index.html)
- [ECharts 配置项手册](https://echarts.apache.org/zh/option.html)
- [ECharts 示例](https://echarts.apache.org/examples/zh/index.html)
- [Vue3 + ECharts 最佳实践](https://github.com/ecomfe/vue-echarts)

## 🎯 最佳实践

1. **按需导入**: 只导入需要的图表类型和组件
2. **主题统一**: 使用统一的主题配置
3. **响应式设计**: 确保图表在不同屏幕尺寸下正常显示
4. **性能优化**: 大数据量时使用数据采样和懒加载
5. **错误处理**: 添加适当的错误处理和加载状态
6. **内存管理**: 及时销毁不需要的图表实例
