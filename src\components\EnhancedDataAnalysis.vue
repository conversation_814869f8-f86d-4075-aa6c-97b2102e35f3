<template>
  <div class="enhanced-data-analysis">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <div class="title-area">
        <h1 class="page-title">数据统计分析</h1>
        <p class="page-subtitle">实时监控产量数据，分析生产趋势</p>
      </div>
      <div class="action-area">
        <n-button-group>
          <n-button type="primary" @click="handleRefresh">
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            刷新
          </n-button>
          <n-button type="info" @click="handleFullscreen">
            <template #icon>
              <n-icon><FullscreenIcon /></n-icon>
            </template>
            全屏
          </n-button>
        </n-button-group>
      </div>
    </div>

    <!-- 标签页导航 -->
    <n-tabs 
      v-model:value="activeTab" 
      type="card" 
      size="large"
      @update:value="handleTabChange"
    >
      <n-tab-pane name="year" tab="年度产量运行分析">
        <template #tab>
          <n-space align="center" :size="8">
            <n-icon><CalendarIcon /></n-icon>
            年度产量运行分析
          </n-space>
        </template>
      </n-tab-pane>
      <n-tab-pane name="month" tab="月度产量运行分析">
        <template #tab>
          <n-space align="center" :size="8">
            <n-icon><TrendingUpIcon /></n-icon>
            月度产量运行分析
          </n-space>
        </template>
      </n-tab-pane>
    </n-tabs>

    <!-- 高级筛选区域 -->
    <n-card class="filter-card" :bordered="false">
      <template #header>
        <n-space align="center" :size="8">
          <n-icon size="18"><FilterIcon /></n-icon>
          <span>筛选条件</span>
        </n-space>
      </template>
      
      <n-form 
        ref="filterFormRef"
        :model="filterForm"
        label-placement="left"
        label-width="auto"
        class="filter-form"
      >
        <n-grid :cols="24" :x-gap="16" :y-gap="12">
          <n-form-item-gi :span="6" label="数据类型" path="type">
            <n-select
              v-model:value="filterForm.type"
              :options="typeOptions"
              placeholder="请选择数据类型"
              clearable
            />
          </n-form-item-gi>
          
          <n-form-item-gi :span="8" label="日期范围" path="dateRange">
            <n-date-picker
              v-model:value="filterForm.dateRange"
              type="daterange"
              placeholder="选择日期范围"
              style="width: 100%"
              clearable
            />
          </n-form-item-gi>
          
          <n-form-item-gi :span="4" label="井类型" path="wellType">
            <n-select
              v-model:value="filterForm.wellType"
              :options="wellTypeOptions"
              placeholder="井类型"
              clearable
            />
          </n-form-item-gi>
          
          <n-form-item-gi :span="6">
            <n-space :size="12">
              <n-button 
                type="primary" 
                @click="handleQuery"
                :loading="loading"
              >
                <template #icon>
                  <n-icon><SearchIcon /></n-icon>
                </template>
                查询
              </n-button>
              
              <n-button @click="handleReset">
                <template #icon>
                  <n-icon><ResetIcon /></n-icon>
                </template>
                重置
              </n-button>
              
              <n-dropdown 
                :options="exportOptions" 
                @select="handleExportSelect"
              >
                <n-button type="success">
                  <template #icon>
                    <n-icon><DownloadIcon /></n-icon>
                  </template>
                  导出
                  <template #icon-right>
                    <n-icon><ChevronDownIcon /></n-icon>
                  </template>
                </n-button>
              </n-dropdown>
            </n-space>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧统计面板 -->
      <div class="stats-panel">
        <n-card title="关键指标" :bordered="false" class="stats-card">
          <template #header-extra>
            <n-tag type="success" size="small">实时更新</n-tag>
          </template>
          
          <!-- 主要指标卡片 -->
          <div class="primary-stats">
            <div class="stat-item plan-stat">
              <div class="stat-icon">
                <n-icon size="32" color="#52c41a">
                  <TargetIcon />
                </n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  <n-number-animation 
                    :from="0" 
                    :to="parseFloat(statisticsData.planOutput)"
                    :precision="2"
                    :duration="1000"
                  />
                  <span class="unit">亿方</span>
                </div>
                <div class="stat-label">计划产量</div>
                <div class="stat-trend">
                  <n-icon size="14" color="#52c41a"><TrendingUpIcon /></n-icon>
                  <span>较上月 +5.2%</span>
                </div>
              </div>
            </div>

            <div class="stat-item actual-stat">
              <div class="stat-icon">
                <n-icon size="32" color="#1890ff">
                  <CheckCircleIcon />
                </n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  <n-number-animation 
                    :from="0" 
                    :to="parseFloat(statisticsData.actualOutput)"
                    :precision="2"
                    :duration="1000"
                  />
                  <span class="unit">亿方</span>
                </div>
                <div class="stat-label">实际产量</div>
                <div class="stat-trend">
                  <n-icon size="14" color="#1890ff"><TrendingUpIcon /></n-icon>
                  <span>较上月 +3.8%</span>
                </div>
              </div>
            </div>

            <div class="stat-item completion-stat">
              <div class="stat-icon">
                <n-icon size="32" color="#fa8c16">
                  <PieChartIcon />
                </n-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">
                  <n-number-animation 
                    :from="0" 
                    :to="parseFloat(statisticsData.completionRate)"
                    :precision="2"
                    :duration="1000"
                  />
                  <span class="unit">%</span>
                </div>
                <div class="stat-label">完成率</div>
                <div class="stat-progress">
                  <n-progress 
                    type="line" 
                    :percentage="parseFloat(statisticsData.completionRate)"
                    :show-indicator="false"
                    :height="6"
                    color="#fa8c16"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 详细统计 -->
          <n-divider />
          
          <div class="detailed-stats">
            <div class="detail-item" v-for="(item, key) in detailedStats" :key="key">
              <div class="detail-header">
                <span class="detail-title">{{ item.title }}</span>
                <n-tag :type="item.trend > 0 ? 'success' : 'error'" size="small">
                  {{ item.trend > 0 ? '+' : '' }}{{ item.trend }}%
                </n-tag>
              </div>
              <div class="detail-content">
                <div class="detail-value">{{ item.value }}<span class="unit">亿方</span></div>
                <div class="detail-meta">
                  <span>实际产量: {{ item.actual }}%</span>
                  <span>超欠: {{ item.excess }}亿方</span>
                </div>
              </div>
              <div class="detail-progress">
                <n-progress 
                  type="line" 
                  :percentage="parseFloat(item.actual)"
                  :show-indicator="false"
                  :height="4"
                  :color="item.color"
                />
              </div>
            </div>
          </div>
        </n-card>
      </div>

      <!-- 右侧图表区域 -->
      <div class="charts-panel">
        <n-card :bordered="false" class="chart-card">
          <template #header>
            <n-space align="center" justify="space-between">
              <n-space align="center" :size="8">
                <n-icon size="18"><BarChartIcon /></n-icon>
                <span>{{ chartTitles[activeTab] }}</span>
              </n-space>
              <n-space :size="8">
                <n-button-group size="small">
                  <n-button 
                    v-for="period in chartPeriods" 
                    :key="period.value"
                    :type="chartPeriod === period.value ? 'primary' : 'default'"
                    @click="chartPeriod = period.value"
                  >
                    {{ period.label }}
                  </n-button>
                </n-button-group>
                <n-button size="small" @click="handleChartFullscreen">
                  <template #icon>
                    <n-icon><ExpandIcon /></n-icon>
                  </template>
                </n-button>
              </n-space>
            </n-space>
          </template>
          
          <div class="chart-container">
            <div 
              ref="mainChartRef" 
              class="main-chart"
              v-loading="chartLoading"
            ></div>
          </div>
        </n-card>

        <n-card :bordered="false" class="chart-card">
          <template #header>
            <n-space align="center" :size="8">
              <n-icon size="18"><LineChartIcon /></n-icon>
              <span>趋势对比分析</span>
            </n-space>
          </template>
          
          <div class="chart-container">
            <div 
              ref="trendChartRef" 
              class="trend-chart"
              v-loading="chartLoading"
            ></div>
          </div>
        </n-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import {
  NTabs,
  NTabPane,
  NCard,
  NForm,
  NFormItemGi,
  NGrid,
  NSelect,
  NDatePicker,
  NButton,
  NButtonGroup,
  NDropdown,
  NSpace,
  NIcon,
  NTag,
  NNumberAnimation,
  NProgress,
  NDivider,
  useMessage
} from 'naive-ui'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

// 图标组件 (这里需要根据实际使用的图标库调整)
const RefreshIcon = () => h('div', '🔄')
const FullscreenIcon = () => h('div', '⛶')
const CalendarIcon = () => h('div', '📅')
const TrendingUpIcon = () => h('div', '📈')
const FilterIcon = () => h('div', '🔍')
const SearchIcon = () => h('div', '🔍')
const ResetIcon = () => h('div', '🔄')
const DownloadIcon = () => h('div', '⬇️')
const ChevronDownIcon = () => h('div', '▼')
const TargetIcon = () => h('div', '🎯')
const CheckCircleIcon = () => h('div', '✅')
const PieChartIcon = () => h('div', '📊')
const BarChartIcon = () => h('div', '📊')
const LineChartIcon = () => h('div', '📈')
const ExpandIcon = () => h('div', '⛶')

const message = useMessage()

// 响应式数据
const activeTab = ref('year')
const loading = ref(false)
const chartLoading = ref(false)
const chartPeriod = ref('month')
const mainChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
let mainChart: ECharts | null = null
let trendChart: ECharts | null = null

// 表单数据
const filterForm = reactive({
  type: null,
  dateRange: null,
  wellType: null
})

// 选项数据
const typeOptions = [
  { label: '全部类型', value: null },
  { label: '计划产量', value: 'plan' },
  { label: '实际产量', value: 'actual' },
  { label: '累计产量', value: 'cumulative' }
]

const wellTypeOptions = [
  { label: '全部井型', value: null },
  { label: '旧井', value: 'old' },
  { label: '新井', value: 'new' },
  { label: '当年新井', value: 'current' }
]

const exportOptions = [
  { label: '导出Excel', key: 'excel', icon: () => h('div', '📊') },
  { label: '导出PDF', key: 'pdf', icon: () => h('div', '📄') },
  { label: '导出图片', key: 'image', icon: () => h('div', '🖼️') }
]

const chartPeriods = [
  { label: '月度', value: 'month' },
  { label: '季度', value: 'quarter' },
  { label: '年度', value: 'year' }
]

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailedStats = computed(() => ({
  oldWell: {
    title: '旧井',
    value: '73.89',
    actual: '91.36',
    excess: '+45.42',
    trend: 2.5,
    color: '#52c41a'
  },
  newWell: {
    title: '上半年新井',
    value: '39.01',
    actual: '98.72',
    excess: '+20.83',
    trend: 5.2,
    color: '#1890ff'
  },
  currentWell: {
    title: '当年新井',
    value: '29.11',
    actual: '98.87',
    excess: '+24.31',
    trend: -1.8,
    color: '#fa8c16'
  }
}))

// 图表标题
const chartTitles = {
  year: '2025年计划实际月度产量及累计产量对比图',
  month: '2025年月度与年度计划对比曲线图'
}

// 方法
const handleTabChange = (value: string) => {
  activeTab.value = value
  nextTick(() => {
    initCharts()
  })
}

const handleQuery = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('查询成功')
    await initCharts()
  } catch (error) {
    message.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.assign(filterForm, {
    type: null,
    dateRange: null,
    wellType: null
  })
  handleQuery()
}

const handleRefresh = () => {
  handleQuery()
}

const handleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const handleChartFullscreen = () => {
  // 图表全屏逻辑
  message.info('图表全屏功能')
}

const handleExportSelect = (key: string) => {
  switch (key) {
    case 'excel':
      exportToExcel()
      break
    case 'pdf':
      exportToPDF()
      break
    case 'image':
      exportToImage()
      break
  }
}

const exportToExcel = () => {
  message.success('Excel导出成功')
}

const exportToPDF = () => {
  message.success('PDF导出成功')
}

const exportToImage = () => {
  if (mainChart) {
    const url = mainChart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    const link = document.createElement('a')
    link.download = '产量分析图表.png'
    link.href = url
    link.click()
    message.success('图片导出成功')
  }
}

// 初始化图表
const initCharts = async () => {
  chartLoading.value = true
  try {
    await nextTick()
    initMainChart()
    initTrendChart()
  } finally {
    chartLoading.value = false
  }
}

const initMainChart = () => {
  if (!mainChartRef.value) return

  if (mainChart) {
    mainChart.dispose()
  }

  mainChart = echarts.init(mainChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: { color: '#333' }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 20,
      textStyle: { fontSize: 12 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisPointer: { type: 'shadow' },
      axisLabel: { fontSize: 11 }
    }],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: { formatter: '{value}', fontSize: 11 },
        nameTextStyle: { fontSize: 12 }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: { formatter: '{value}', fontSize: 11 },
        nameTextStyle: { fontSize: 12 }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: { focus: 'series' }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#a6e7a6' },
            { offset: 0.5, color: '#52c41a' },
            { offset: 1, color: '#52c41a' }
          ])
        },
        emphasis: { focus: 'series' }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
        itemStyle: { color: '#fa8c16' },
        lineStyle: { width: 3 },
        symbol: 'circle',
        symbolSize: 6,
        emphasis: { focus: 'series' }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
        itemStyle: { color: '#f5222d' },
        lineStyle: { width: 3 },
        symbol: 'circle',
        symbolSize: 6,
        emphasis: { focus: 'series' }
      }
    ],
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  mainChart.setOption(option)
}

const initTrendChart = () => {
  if (!trendChartRef.value) return

  if (trendChart) {
    trendChart.dispose()
  }

  trendChart = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: { color: '#333' }
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 20,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisLabel: { fontSize: 11 }
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)',
      axisLabel: { fontSize: 11 },
      nameTextStyle: { fontSize: 12 }
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid', width: 2 },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid', width: 2 },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed', width: 2 },
        symbol: 'triangle',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed', width: 2 },
        symbol: 'triangle',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted', width: 2 },
        symbol: 'diamond',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted', width: 2 },
        symbol: 'diamond',
        symbolSize: 4,
        emphasis: { focus: 'series' }
      }
    ],
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  trendChart.setOption(option)
}

// 监听窗口大小变化
const handleResize = () => {
  if (mainChart) {
    mainChart.resize()
  }
  if (trendChart) {
    trendChart.resize()
  }
}

// 生命周期
onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

// 监听图表周期变化
watch(chartPeriod, () => {
  initCharts()
})
</script>

<style scoped>
.enhanced-data-analysis {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.title-area .page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 4px 0;
}

.title-area .page-subtitle {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.filter-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-form {
  margin-top: 16px;
}

.main-content {
  display: flex;
  gap: 24px;
  height: calc(100vh - 300px);
}

.stats-panel {
  width: 350px;
  flex-shrink: 0;
}

.charts-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.primary-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.plan-stat {
  border-left: 4px solid #52c41a;
}

.actual-stat {
  border-left: 4px solid #1890ff;
}

.completion-stat {
  border-left: 4px solid #fa8c16;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-value .unit {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 400;
  margin-left: 4px;
}

.stat-label {
  font-size: 14px;
  color: #595959;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #52c41a;
}

.stat-progress {
  margin-top: 8px;
}

.detailed-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.detail-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.detail-content {
  margin-bottom: 12px;
}

.detail-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.detail-value .unit {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 400;
}

.detail-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.chart-card {
  flex: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-container {
  height: 100%;
  min-height: 300px;
}

.main-chart,
.trend-chart {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .stats-panel {
    width: 100%;
  }

  .primary-stats {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-item {
    flex: 1;
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .enhanced-data-analysis {
    padding: 16px;
  }

  .header-section {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .primary-stats {
    flex-direction: column;
  }

  .detailed-stats {
    flex-direction: column;
  }
}
</style>
