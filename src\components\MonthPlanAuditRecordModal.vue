<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="审核记录"
    style="width: 80%; max-width: 1000px;"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="audit-record-modal">
      <!-- 计划信息 -->
      <div class="plan-info">
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item label="计划名称">
            {{ planInfo.planName }}
          </n-descriptions-item>
          <n-descriptions-item label="提交人">
            {{ planInfo.submitter }}
          </n-descriptions-item>
          <n-descriptions-item label="提交时间">
            {{ planInfo.submitTime }}
          </n-descriptions-item>
          <n-descriptions-item label="当前状态">
            <n-tag :type="getStatusType(planInfo.status)" size="small">
              {{ planInfo.status }}
            </n-tag>
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <!-- 审核记录表格 -->
      <div class="audit-table-section">
        <h4>审核记录</h4>
        <n-data-table
          :columns="columns"
          :data="auditRecords"
          :pagination="false"
          :bordered="true"
          :single-line="false"
          size="small"
          striped
        />
      </div>

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <n-space justify="end">
          <n-button @click="handleClose">
            关闭
          </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, h } from 'vue'
import {
  NModal,
  NDescriptions,
  NDescriptionsItem,
  NDataTable,
  NTag,
  NButton,
  NSpace,
  type DataTableColumns
} from 'naive-ui'

// Props
const props = defineProps<{
  show: boolean
  planData?: any
}>()

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean]
}>()

// 响应式数据
const showModal = ref(props.show)

// 监听props变化
watch(() => props.show, (newVal) => {
  showModal.value = newVal
})

// 监听modal变化
watch(showModal, (newVal) => {
  emits('update:show', newVal)
})

// 计划信息
const planInfo = reactive({
  planName: props.planData?.planName || '川东北气分公司2025年05月常规监测月计划',
  submitter: props.planData?.submitter || '廖华伟',
  submitTime: props.planData?.submitTime || '2025-04-29 09:26:26',
  status: props.planData?.status || '审核中'
})

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '审核通过':
      return 'success'
    case '审核中':
      return 'warning'
    case '待提交':
      return 'info'
    case '审核不通过':
      return 'error'
    default:
      return 'default'
  }
}

// 获取审核结果标签类型
const getAuditResultType = (result: string) => {
  switch (result) {
    case '同意':
      return 'success'
    case '不同意':
      return 'error'
    case '待审核':
      return 'warning'
    default:
      return 'default'
  }
}

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_, index) => index + 1
  },
  {
    title: '审核人',
    key: 'auditor',
    width: 100
  },
  {
    title: '审核时间',
    key: 'auditTime',
    width: 160
  },
  {
    title: '审核意见',
    key: 'auditOpinion',
    width: 300,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '审核结果',
    key: 'auditResult',
    width: 100,
    render: (row: any) => {
      return h(NTag, {
        type: getAuditResultType(row.auditResult),
        size: 'small'
      }, { default: () => row.auditResult })
    }
  }
]

// 模拟审核记录数据
const auditRecords = ref([
  {
    auditor: '张三',
    auditTime: '2025-04-30 10:30:00',
    auditOpinion: '计划内容详实，时间安排合理，建议通过审核。',
    auditResult: '同意'
  },
  {
    auditor: '李四',
    auditTime: '2025-04-29 16:45:00',
    auditOpinion: '初步审核通过，需要进一步确认预算部分的合理性。',
    auditResult: '同意'
  },
  {
    auditor: '王五',
    auditTime: '2025-04-29 14:20:00',
    auditOpinion: '提交的计划格式规范，内容完整，符合要求。',
    auditResult: '同意'
  }
])

// 方法
const handleClose = () => {
  showModal.value = false
}

// 监听planData变化，更新计划信息
watch(() => props.planData, (newData) => {
  if (newData) {
    planInfo.planName = newData.planName || planInfo.planName
    planInfo.submitter = newData.submitter || planInfo.submitter
    planInfo.submitTime = newData.submitTime || planInfo.submitTime
    planInfo.status = newData.status || planInfo.status
  }
}, { deep: true })
</script>

<style scoped>
.audit-record-modal {
  padding: 0;
}

.plan-info {
  margin-bottom: 20px;
}

.audit-table-section {
  margin-bottom: 20px;
}

.audit-table-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.footer-buttons {
  padding: 16px 0 0 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px;
}

/* 描述列表样式 */
:deep(.n-descriptions-item-label) {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .audit-record-modal {
    padding: 8px;
  }
  
  .audit-table-section h4 {
    font-size: 14px;
  }
}
</style>
