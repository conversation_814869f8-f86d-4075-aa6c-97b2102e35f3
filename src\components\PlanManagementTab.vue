<template>
  <div class="plan-management-tab">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-space align="center" :size="16">
        <div class="filter-item">
          <span class="filter-label">单位:</span>
          <n-select
            v-model:value="selectedUnit"
            :options="unitOptions"
            placeholder="全部"
            style="width: 120px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">时间:</span>
          <n-date-picker
            v-model:value="selectedYear"
            type="year"
            placeholder="2025年"
            style="width: 100px"
          />
        </div>

        <div class="filter-item">
          <span class="filter-label">版本选择:</span>
          <n-select
            v-model:value="selectedVersion"
            :options="versionOptions"
            placeholder="年初版本"
            style="width: 120px"
          />
        </div>

        <n-button type="primary" @click="handleSearch"> 查询 </n-button>
        <n-button type="primary" @click="handleAddPlan">
          新增/修改计划
        </n-button>
      </n-space>
    </div>

    <!-- 计划标题 -->
    <div class="plan-title">
      <h3>2025年措施计划</h3>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        :bordered="true"
        :single-line="false"
        striped
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import {
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  NAlert,
  type DataTableColumns,
} from 'naive-ui';

// 响应式数据
const selectedYear = ref(new Date().getFullYear());
const selectedUnit = ref(null);
const selectedVersion = ref(null);
const showStatistics = ref(true);

// 下拉选项
const unitOptions = [
  { label: '全部', value: undefined, type: 'option' },
  { label: '川中油气矿', value: '川中油气矿', type: 'option' },
  { label: '重庆气矿', value: '重庆气矿', type: 'option' },
  { label: '川西北气矿', value: '川西北气矿', type: 'option' },
];

const versionOptions = [
  { label: '年初版本', value: 'initial', type: 'option' },
  { label: '年中版本', value: 'mid', type: 'option' },
  { label: '年末版本', value: 'final', type: 'option' },
];
// 表格列定义
const columns: DataTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_, index) => index + 1,
  },
  {
    title: '年月',
    key: 'yearMonth',
    width: 80,
  },
  {
    title: '单位',
    key: 'unit',
    width: 120,
  },
  {
    title: '名称',
    key: 'name',
    width: 150,
    render: (row: any) => {
      if (row.name && row.planType) {
        return `${row.name} ${row.planType}`;
      }
      return row.name || '';
    },
  },
  {
    title: '提交人',
    key: 'proposer',
    width: 80,
  },
  {
    title: '提交时间',
    key: 'proposeTime',
    width: 140,
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 80,
    render: (row: any) => {
      return row.auditStatus || '进行中';
    },
  },
  {
    title: '审核记录',
    key: 'auditRecord',
    width: 80,
    render: () => {
      return '查看';
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: () => {
      return '审批';
    },
  },
];

// 模拟表格数据
const tableData = ref([
  {
    yearMonth: '2025年',
    unit: '川中油气矿',
    name: '蓬莱23-H1',
    planType: '2025年推进计划',
    proposer: '***',
    proposeTime: '2025/4/28 14:13:30',
    auditStatus: '进行中',
    auditRecord: '查看',
    actions: '详情',
  },
  {
    yearMonth: '2025年',
    unit: '重庆气矿公司',
    name: '',
    planType: '',
    proposer: '***',
    proposeTime: '2025/4/27 20:13:30',
    auditStatus: '进行中',
    auditRecord: '查看',
    actions: '详情',
  },
  {
    yearMonth: '2025年',
    unit: '重庆气矿',
    name: '蓬莱31-H2',
    planType: '2025年推进计划',
    proposer: '***',
    proposeTime: '2025/4/28 14:13:30',
    auditStatus: '进行中',
    auditRecord: '查看',
    actions: '详情',
  },
  {
    yearMonth: '2025年',
    unit: '川中油气矿',
    name: '川中北气矿',
    planType: '2025年推进计划',
    proposer: '***',
    proposeTime: '2025/4/25 10:13:30',
    auditStatus: '已完成',
    auditRecord: '查看',
    actions: '详情',
  },
  {
    yearMonth: '2025年',
    unit: '川西北气矿',
    name: '',
    planType: '',
    proposer: '***',
    proposeTime: '2025/4/23 15:13:30',
    auditStatus: '已完成',
    auditRecord: '查看',
    actions: '详情',
  },
  {
    yearMonth: '2025年',
    unit: '川中油气矿',
    name: '',
    planType: '',
    proposer: '***',
    proposeTime: '2025/4/30 18:13:30',
    auditStatus: '已完成',
    auditRecord: '查看',
    actions: '详情',
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 统计数据
const totalPlans = computed(() => tableData.value.length);
const completedPlans = ref(3);

// 方法
const handleSearch = () => {
  console.log('执行查询', {
    year: selectedYear.value,
    unit: selectedUnit.value,
    version: selectedVersion.value,
  });
};

const handleAddPlan = () => {
  console.log('新增/修改计划');
};
</script>

<style scoped>
.plan-management-tab {
  width: 100%;
  background-color: #fff;
}

.action-buttons {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
}

.filter-section {
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 60px;
}

.plan-title {
  margin-bottom: 16px;
  padding: 0 16px;
  text-align: center;
}

.plan-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.statistics-alert {
  margin-bottom: 16px;
  padding: 0 16px;
}

.table-section {
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  padding: 16px;
}
</style>
