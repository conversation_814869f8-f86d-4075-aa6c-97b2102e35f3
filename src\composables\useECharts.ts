import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import type { Ref } from 'vue'
import type { ECharts, EChartsOption } from 'echarts'
import { createChart, disposeChart, handleChartResize } from '@/utils/echarts'

export interface UseEChartsOptions {
  theme?: string
  autoResize?: boolean
  loadingOptions?: {
    text?: string
    color?: string
    textColor?: string
    maskColor?: string
  }
}

export function useECharts(
  elRef: Ref<HTMLElement | undefined>,
  options: UseEChartsOptions = {}
) {
  const {
    theme = 'custom',
    autoResize = true,
    loadingOptions = {
      text: '加载中...',
      color: '#1890ff',
      textColor: '#1890ff',
      maskColor: 'rgba(255, 255, 255, 0.8)'
    }
  } = options

  const chartInstance = ref<ECharts | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 初始化图表
  const initChart = async (): Promise<void> => {
    if (!elRef.value) {
      error.value = '图表容器元素不存在'
      return
    }

    try {
      // 销毁已存在的图表实例
      if (chartInstance.value) {
        disposeChart(chartInstance.value)
      }

      // 创建新的图表实例
      chartInstance.value = createChart(elRef.value, theme)
      error.value = null
    } catch (err) {
      error.value = `图表初始化失败: ${err}`
      console.error('ECharts initialization failed:', err)
    }
  }

  // 设置图表选项
  const setOption = (option: EChartsOption, notMerge = false, lazyUpdate = false): void => {
    if (!chartInstance.value) {
      error.value = '图表实例不存在，请先初始化图表'
      return
    }

    try {
      chartInstance.value.setOption(option, notMerge, lazyUpdate)
      error.value = null
    } catch (err) {
      error.value = `设置图表选项失败: ${err}`
      console.error('ECharts setOption failed:', err)
    }
  }

  // 显示加载状态
  const showLoading = (text?: string): void => {
    if (!chartInstance.value) return

    isLoading.value = true
    chartInstance.value.showLoading('default', {
      ...loadingOptions,
      text: text || loadingOptions.text
    })
  }

  // 隐藏加载状态
  const hideLoading = (): void => {
    if (!chartInstance.value) return

    isLoading.value = false
    chartInstance.value.hideLoading()
  }

  // 调整图表大小
  const resize = (): void => {
    if (chartInstance.value) {
      handleChartResize(chartInstance.value)
    }
  }

  // 获取图表实例
  const getInstance = (): ECharts | null => {
    return chartInstance.value
  }

  // 获取图表数据URL
  const getDataURL = (options?: {
    type?: 'png' | 'jpeg'
    pixelRatio?: number
    backgroundColor?: string
  }): string => {
    if (!chartInstance.value) {
      throw new Error('图表实例不存在')
    }

    return chartInstance.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
      ...options
    })
  }

  // 导出图表为图片
  const exportImage = (filename: string = 'chart', options?: {
    type?: 'png' | 'jpeg'
    pixelRatio?: number
    backgroundColor?: string
  }): void => {
    try {
      const url = getDataURL(options)
      const link = document.createElement('a')
      link.download = `${filename}.${options?.type || 'png'}`
      link.href = url
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (err) {
      error.value = `导出图片失败: ${err}`
      console.error('Export image failed:', err)
    }
  }

  // 清空图表
  const clear = (): void => {
    if (chartInstance.value) {
      chartInstance.value.clear()
    }
  }

  // 销毁图表
  const dispose = (): void => {
    if (chartInstance.value) {
      disposeChart(chartInstance.value)
      chartInstance.value = null
    }
  }

  // 窗口大小变化处理
  const handleWindowResize = (): void => {
    resize()
  }

  // 生命周期管理
  onMounted(async () => {
    await nextTick()
    await initChart()

    if (autoResize) {
      window.addEventListener('resize', handleWindowResize)
    }
  })

  onUnmounted(() => {
    dispose()
    
    if (autoResize) {
      window.removeEventListener('resize', handleWindowResize)
    }
  })

  // 监听容器元素变化
  watch(
    elRef,
    async (newEl) => {
      if (newEl) {
        await nextTick()
        await initChart()
      }
    },
    { flush: 'post' }
  )

  return {
    // 状态
    chartInstance: chartInstance.value,
    isLoading,
    error,

    // 方法
    initChart,
    setOption,
    showLoading,
    hideLoading,
    resize,
    getInstance,
    getDataURL,
    exportImage,
    clear,
    dispose
  }
}

// 专门用于数据分析图表的组合式函数
export function useDataAnalysisCharts() {
  const monthlyChartRef = ref<HTMLElement>()
  const trendChartRef = ref<HTMLElement>()

  const monthlyChart = useECharts(monthlyChartRef, {
    theme: 'custom',
    autoResize: true
  })

  const trendChart = useECharts(trendChartRef, {
    theme: 'custom', 
    autoResize: true
  })

  // 更新月度产量图表
  const updateMonthlyChart = (data: {
    months: string[]
    planOutput: number[]
    actualOutput: number[]
    cumulativePlan: number[]
    cumulativeActual: number[]
  }): void => {
    const option: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: { color: '#999' }
        }
      },
      legend: {
        data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
        top: 10,
        textStyle: { fontSize: 12 }
      },
      xAxis: [{
        type: 'category',
        data: data.months,
        axisPointer: { type: 'shadow' }
      }],
      yAxis: [
        {
          type: 'value',
          name: '月度产量(亿方)',
          position: 'left',
          axisLabel: { formatter: '{value}' }
        },
        {
          type: 'value',
          name: '累计产量(亿方)',
          position: 'right',
          axisLabel: { formatter: '{value}' }
        }
      ],
      series: [
        {
          name: '年度计划月度产量',
          type: 'bar',
          data: data.planOutput,
          itemStyle: { color: '#5470c6' }
        },
        {
          name: '年度实际月度产量',
          type: 'bar',
          data: data.actualOutput,
          itemStyle: { color: '#91cc75' }
        },
        {
          name: '年度计划累计产量',
          type: 'line',
          yAxisIndex: 1,
          data: data.cumulativePlan,
          itemStyle: { color: '#fac858' },
          lineStyle: { width: 3 }
        },
        {
          name: '实际累计产量',
          type: 'line',
          yAxisIndex: 1,
          data: data.cumulativeActual,
          itemStyle: { color: '#ee6666' },
          lineStyle: { width: 3 }
        }
      ]
    }

    monthlyChart.setOption(option)
  }

  // 更新趋势对比图表
  const updateTrendChart = (data: {
    months: string[]
    currentWellPlan: number[]
    currentWellActual: number[]
    firstHalfWellPlan: number[]
    firstHalfWellActual: number[]
    oldWellPlan: number[]
    oldWellActual: number[]
  }): void => {
    const option: EChartsOption = {
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
        top: 10,
        textStyle: { fontSize: 11 }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.months
      },
      yAxis: {
        type: 'value',
        name: '产量(亿方)'
      },
      series: [
        {
          name: '当年新井计划产量',
          type: 'line',
          data: data.currentWellPlan,
          itemStyle: { color: '#5470c6' },
          lineStyle: { type: 'solid' }
        },
        {
          name: '当年新井实际产量',
          type: 'line',
          data: data.currentWellActual,
          itemStyle: { color: '#91cc75' },
          lineStyle: { type: 'solid' }
        },
        {
          name: '上半年新井计划产量',
          type: 'line',
          data: data.firstHalfWellPlan,
          itemStyle: { color: '#fac858' },
          lineStyle: { type: 'dashed' }
        },
        {
          name: '上半年新井实际产量',
          type: 'line',
          data: data.firstHalfWellActual,
          itemStyle: { color: '#ee6666' },
          lineStyle: { type: 'dashed' }
        },
        {
          name: '旧井计划产量',
          type: 'line',
          data: data.oldWellPlan,
          itemStyle: { color: '#73c0de' },
          lineStyle: { type: 'dotted' }
        },
        {
          name: '旧井实际产量',
          type: 'line',
          data: data.oldWellActual,
          itemStyle: { color: '#3ba272' },
          lineStyle: { type: 'dotted' }
        }
      ]
    }

    trendChart.setOption(option)
  }

  return {
    // 引用
    monthlyChartRef,
    trendChartRef,

    // 图表实例
    monthlyChart,
    trendChart,

    // 更新方法
    updateMonthlyChart,
    updateTrendChart
  }
}
