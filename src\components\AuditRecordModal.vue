<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="审核记录"
    style="width: 80%; max-width: 800px;"
    :mask-closable="true"
    :closable="true"
    @close="handleClose"
  >
    <div class="audit-record-modal">
      <!-- 审核记录表格 -->
      <div class="table-section">
        <n-data-table
          :columns="columns"
          :data="auditRecords"
          :pagination="false"
          :bordered="true"
          :single-line="false"
          size="small"
          striped
        />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, h } from 'vue'
import {
  NModal,
  NDataTable,
  type DataTableColumns
} from 'naive-ui'

// Props
const props = defineProps<{
  show: boolean
  planId?: string | number
}>()

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean]
}>()

// 响应式数据
const showModal = ref(props.show)

// 监听props变化
watch(() => props.show, (newVal) => {
  showModal.value = newVal
})

// 监听modal变化
watch(showModal, (newVal) => {
  emits('update:show', newVal)
})

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '审核人',
    key: 'auditor',
    width: 120,
    align: 'center'
  },
  {
    title: '审核时间',
    key: 'auditTime',
    width: 180,
    align: 'center'
  },
  {
    title: '审核意见',
    key: 'auditOpinion',
    width: 120,
    align: 'center'
  },
  {
    title: '审核结果',
    key: 'auditResult',
    width: 120,
    align: 'center',
    render: (row: any) => {
      return row.auditResult === '审核通过'
        ? h('span', { style: { color: '#52c41a' } }, row.auditResult)
        : h('span', { style: { color: '#ff4d4f' } }, row.auditResult)
    }
  }
]

// 模拟审核记录数据
const auditRecords = ref([
  {
    auditor: '杨洋',
    auditTime: '2025-02-24 11:51:37',
    auditOpinion: '同意',
    auditResult: '审核通过'
  },
  {
    auditor: '欧家强',
    auditTime: '2025-03-03 09:36:14',
    auditOpinion: '同意',
    auditResult: '审核通过'
  },
  {
    auditor: '甘笑非',
    auditTime: '2025-03-04 10:08:13',
    auditOpinion: '同意',
    auditResult: '审核通过'
  }
])

// 方法
const handleClose = () => {
  showModal.value = false
}

// 根据planId加载审核记录（可选）
watch(() => props.planId, (newPlanId) => {
  if (newPlanId && props.show) {
    loadAuditRecords(newPlanId)
  }
})

const loadAuditRecords = (planId: string | number) => {
  console.log('加载计划审核记录:', planId)
  // 这里可以根据planId从API加载具体的审核记录
}
</script>

<style scoped>
.audit-record-modal {
  padding: 0;
}

.table-section {
  max-height: 400px;
  overflow: auto;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-section {
    max-height: 300px;
  }
}
</style>
