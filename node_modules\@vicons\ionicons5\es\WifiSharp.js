import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M332.69 320a115 115 0 0 0-152.8 0',
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-linejoin': 'round',
    'stroke-width': '42'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M393.74 259a201.26 201.26 0 0 0-274.92 0',
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-linejoin': 'round',
    'stroke-width': '42'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M448 191.52a288 288 0 0 0-383.44 0',
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-linejoin': 'round',
    'stroke-width': '42'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_5 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M300.67 384L256 433l-44.34-49a56.73 56.73 0 0 1 88.92 0z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_6 = [_hoisted_2, _hoisted_3, _hoisted_4, _hoisted_5]
export default defineComponent({
  name: 'WifiSharp',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_6)
  }
})
