<template>
  <div class="optimized-data-analysis">
    <!-- 顶部标签页 -->
    <div class="tabs-header">
      <div class="tab-item" :class="{ active: activeTab === 'year' }" @click="switchTab('year')">
        年度产量运行分析
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'month' }" @click="switchTab('month')">
        月度产量运行分析
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">共类型:</span>
        <select v-model="filterData.type" class="filter-select" @change="handleFilterChange">
          <option value="">请选择类型</option>
          <option value="plan">计划产量</option>
          <option value="actual">实际产量</option>
          <option value="cumulative">累计产量</option>
        </select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">日期选择:</span>
        <input 
          type="date" 
          v-model="filterData.startDate" 
          class="filter-input"
          @change="handleFilterChange"
        />
        <span class="date-separator">至</span>
        <input 
          type="date" 
          v-model="filterData.endDate" 
          class="filter-input"
          @change="handleFilterChange"
        />
      </div>
      
      <div class="filter-actions">
        <button class="btn btn-primary" @click="handleQuery" :disabled="loading">
          {{ loading ? '查询中...' : '查询' }}
        </button>
        <button class="btn btn-success" @click="handleExport">导出</button>
        <button class="btn btn-info" @click="exportCharts">导出图表</button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 左侧统计卡片 -->
      <div class="left-stats">
        <!-- 主要指标卡片 -->
        <div class="stat-card primary-card plan-card">
          <div class="card-icon">
            <div class="icon-circle green">
              <span class="icon">📊</span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statisticsData.planOutput }}</div>
            <div class="card-unit">亿方</div>
            <div class="card-label">计划产量</div>
          </div>
        </div>

        <div class="stat-card primary-card actual-card">
          <div class="card-icon">
            <div class="icon-circle orange">
              <span class="icon">📦</span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statisticsData.actualOutput }}</div>
            <div class="card-unit">亿方</div>
            <div class="card-label">实际产量</div>
          </div>
        </div>

        <div class="stat-card primary-card completion-card">
          <div class="card-icon">
            <div class="icon-circle red">
              <span class="icon">⏱️</span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ statisticsData.completionRate }}</div>
            <div class="card-unit">%</div>
            <div class="card-label">产量完成率</div>
          </div>
        </div>

        <!-- 详细统计卡片 -->
        <div class="detail-stats">
          <div class="detail-card old-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.oldWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">旧井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.oldWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value positive">{{ detailStats.oldWell.excess }}亿方</span>
              </div>
            </div>
          </div>

          <div class="detail-card new-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.newWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">上半年新井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.newWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value positive">{{ detailStats.newWell.excess }}亿方</span>
              </div>
            </div>
          </div>

          <div class="detail-card current-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.currentWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">当年新井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.currentWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value negative">{{ detailStats.currentWell.excess }}亿方</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="right-charts">
        <!-- 第一个图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-title">
              <span class="title-icon">🔵</span>
              2025年计划实际月度产量及累计产量对比图
            </div>
            <div class="chart-actions">
              <button class="chart-btn" @click="exportMonthlyChart" title="导出图表">📤</button>
              <button class="chart-btn" @click="refreshMonthlyChart" title="刷新图表">🔄</button>
            </div>
          </div>
          <div class="chart-container">
            <div 
              ref="monthlyChartRef" 
              class="chart-content"
              v-loading="monthlyChart.isLoading.value"
            ></div>
            <div v-if="monthlyChart.error.value" class="chart-error">
              {{ monthlyChart.error.value }}
            </div>
          </div>
        </div>

        <!-- 第二个图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-title">
              <span class="title-icon">🔵</span>
              2025年月度与年度计划对比曲线图
            </div>
            <div class="chart-actions">
              <button class="chart-btn" @click="exportTrendChart" title="导出图表">📤</button>
              <button class="chart-btn" @click="refreshTrendChart" title="刷新图表">🔄</button>
            </div>
          </div>
          <div class="chart-container">
            <div 
              ref="trendChartRef" 
              class="chart-content"
              v-loading="trendChart.isLoading.value"
            ></div>
            <div v-if="trendChart.error.value" class="chart-error">
              {{ trendChart.error.value }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import {
  NTabs,
  NTabPane,
  NCard,
  NSpace,
  NSelect,
  NDatePicker,
  NButton,
  NButtonGroup,
  NIcon,
  NStatistic,
  NProgress,
  NTag,
  useMessage
} from 'naive-ui'
import { useDataAnalysisCharts } from '@/composables/useECharts'

const message = useMessage()

// 响应式数据
const activeTab = ref('year')
const loading = ref(false)

// 使用 ECharts 组合式函数
const {
  monthlyChartRef,
  trendChartRef,
  monthlyChart,
  trendChart,
  updateMonthlyChart,
  updateTrendChart
} = useDataAnalysisCharts()

// 筛选数据
const filterData = reactive({
  type: '',
  startDate: '',
  endDate: ''
})

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailStats = reactive({
  oldWell: {
    value: '73.89',
    rate: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    rate: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    rate: '98.87',
    excess: '24.31'
  }
})

// 模拟数据
const mockData = {
  months: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
           '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
  planOutput: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
  actualOutput: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
  cumulativePlan: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
  cumulativeActual: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
  currentWellPlan: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
  currentWellActual: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
  firstHalfWellPlan: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
  firstHalfWellActual: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
  oldWellPlan: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
  oldWellActual: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2]
}

// 方法
const switchTab = (tab: string) => {
  activeTab.value = tab
  loadChartData()
}

const handleFilterChange = () => {
  // 筛选条件变化时的处理
  console.log('筛选条件变化:', filterData)
}

const handleQuery = async () => {
  loading.value = true
  
  try {
    // 显示加载状态
    monthlyChart.showLoading('正在加载数据...')
    trendChart.showLoading('正在加载数据...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 加载图表数据
    loadChartData()
    
    console.log('查询完成', filterData)
  } catch (error) {
    console.error('查询失败:', error)
  } finally {
    loading.value = false
    monthlyChart.hideLoading()
    trendChart.hideLoading()
  }
}

const handleExport = () => {
  console.log('导出数据')
  // 这里可以实现数据导出逻辑
}

const exportCharts = () => {
  exportMonthlyChart()
  exportTrendChart()
}

const exportMonthlyChart = () => {
  monthlyChart.exportImage('月度产量对比图', {
    type: 'png',
    backgroundColor: '#fff'
  })
}

const exportTrendChart = () => {
  trendChart.exportImage('趋势对比图', {
    type: 'png',
    backgroundColor: '#fff'
  })
}

const refreshMonthlyChart = () => {
  monthlyChart.showLoading('刷新中...')
  setTimeout(() => {
    updateMonthlyChart(mockData)
    monthlyChart.hideLoading()
  }, 500)
}

const refreshTrendChart = () => {
  trendChart.showLoading('刷新中...')
  setTimeout(() => {
    updateTrendChart(mockData)
    trendChart.hideLoading()
  }, 500)
}

const loadChartData = () => {
  // 更新图表数据
  updateMonthlyChart(mockData)
  updateTrendChart(mockData)
}

// 生命周期
onMounted(() => {
  // 初始化时加载数据
  setTimeout(() => {
    loadChartData()
  }, 100)
})

// 监听标签页变化
watch(activeTab, () => {
  // 标签页切换时可以加载不同的数据
  loadChartData()
})
</script>

<style scoped>
.optimized-data-analysis {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 标签页样式 */
.tabs-header {
  display: flex;
  background: white;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  padding: 12px 24px;
  text-align: center;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  border-right: 1px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background: #1890ff;
  color: white;
}

.tab-item:hover:not(.active) {
  background: #e9ecef;
  color: #333;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  font-weight: 500;
}

.filter-select,
.filter-input {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.filter-select:focus,
.filter-input:focus {
  border-color: #1890ff;
}

.filter-select {
  min-width: 120px;
}

.filter-input {
  width: 140px;
}

.date-separator {
  font-size: 14px;
  color: #666;
  margin: 0 4px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.btn {
  padding: 6px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #40a9ff;
}

.btn-success {
  background: #52c41a;
  color: white;
}

.btn-success:hover {
  background: #73d13d;
}

.btn-info {
  background: #1890ff;
  color: white;
}

.btn-info:hover {
  background: #40a9ff;
}

/* 主要内容区域 */
.content-area {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

/* 左侧统计区域 */
.left-stats {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 主要统计卡片 */
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.primary-card {
  min-height: 80px;
}

.plan-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.completion-card {
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

.icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.icon-circle.green {
  background: #52c41a;
  color: white;
}

.icon-circle.orange {
  background: #fa8c16;
  color: white;
}

.icon-circle.red {
  background: #f5222d;
  color: white;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.card-unit {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 详细统计卡片 */
.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.detail-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #1890ff;
}

.old-well-card {
  border-left-color: #52c41a;
}

.new-well-card {
  border-left-color: #1890ff;
}

.current-well-card {
  border-left-color: #fa8c16;
}

.detail-main {
  margin-bottom: 12px;
}

.detail-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.detail-value .unit {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.detail-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.info-value.positive {
  color: #52c41a;
}

.info-value.negative {
  color: #f5222d;
}

/* 右侧图表区域 */
.right-charts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.title-icon {
  font-size: 12px;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-btn {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.chart-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.chart-container {
  height: calc(50vh - 120px);
  min-height: 250px;
  position: relative;
}

.chart-content {
  width: 100%;
  height: 100%;
}

.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #f5222d;
  font-size: 14px;
  text-align: center;
}

/* 加载状态 */
[v-loading] {
  position: relative;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
    height: auto;
  }

  .left-stats {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 1;
    min-width: 200px;
  }

  .detail-stats {
    flex-direction: row;
    gap: 16px;
  }

  .detail-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .optimized-data-analysis {
    padding: 12px;
  }

  .filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
  }

  .left-stats {
    flex-direction: column;
  }

  .detail-stats {
    flex-direction: column;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
