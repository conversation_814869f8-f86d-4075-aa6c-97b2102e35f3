<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="新增年计划"
    style="width: 90%; max-width: 1200px"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="plan-form-modal">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <n-space align="center" :size="16">
          <div class="filter-item">
            <span class="filter-label">单位:</span>
            <n-select
              v-model:value="selectedUnit"
              :options="unitOptions"
              placeholder="川中油气矿"
              style="width: 150px"
              size="small"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">年份:</span>
            <n-date-picker
              v-model:value="selectedYear"
              type="year"
              placeholder="2025年"
              style="width: 120px"
              size="small"
            />
          </div>
          <n-button type="primary" size="small"> 新增 </n-button>
          <n-button type="primary" size="small" @click="handleImport">
            导入
          </n-button>
        </n-space>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <n-data-table
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
          :bordered="true"
          :single-line="false"
          size="small"
          striped
          :scroll-x="1000"
        />
      </div>

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <n-space justify="center" :size="16">
          <n-button type="primary" @click="handleSave"> 保存 </n-button>
          <n-button type="info" @click="handleSubmitAudit"> 提交审核 </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, h } from 'vue';
import {
  NModal,
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  type DataTableColumns,
} from 'naive-ui';

// Props
const props = defineProps<{
  show: boolean;
}>();

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean];
  save: [data: any];
  'submit-audit': [data: any];
}>();

// 响应式数据
const showModal = ref(props.show);
const selectedUnit = ref('川中油气矿');
const selectedYear = ref(new Date().getFullYear());

// 监听props变化
watch(
  () => props.show,
  newVal => {
    showModal.value = newVal;
  }
);

// 监听modal变化
watch(showModal, newVal => {
  emits('update:show', newVal);
});

// 下拉选项
const unitOptions = [
  { label: '川中油气矿', value: '川中油气矿' },
  { label: '重庆气矿', value: '重庆气矿' },
  { label: '川西北气矿', value: '川西北气矿' },
];

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_, index) => index + 1,
  },
  {
    title: '年度计划安排',
    key: 'planCategory',
    width: 120,
    render: (row: any) => {
      return row.planCategory || '';
    },
  },
  {
    title: '井号',
    key: 'wellNumber',
    width: 100,
  },
  {
    title: '单位',
    key: 'unit',
    width: 120,
  },
  {
    title: '领域',
    key: 'domain',
    width: 100,
  },
  {
    title: '措施类型',
    key: 'measureType',
    width: 120,
    render: (row: any) => {
      // 高亮显示某些单元格
      if (row.highlighted) {
        return h(
          'span',
          {
            style: {
              backgroundColor: '#fff3cd',
              padding: '2px 4px',
              borderRadius: '2px',
            },
          },
          row.measureType || ''
        );
      }
      return row.measureType || '';
    },
  },
];

// 模拟表格数据
const tableData = ref([
  {
    planCategory: '2025-1',
    wellNumber: '',
    unit: '',
    domain: '',
    measureType: '',
    highlighted: true,
  },
  {
    planCategory: '2025-3',
    wellNumber: '',
    unit: '',
    domain: '',
    measureType: '运井号',
    highlighted: true,
  },
  {
    planCategory: '2025-5',
    wellNumber: '',
    unit: '',
    domain: '',
    measureType: '',
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 方法
const handleImport = () => {
  console.log('导入数据', {
    unit: selectedUnit.value,
    year: selectedYear.value,
  });
};

const handleSave = () => {
  console.log('保存数据');
  emits('save', {
    unit: selectedUnit.value,
    year: selectedYear.value,
    data: tableData.value,
  });
};

const handleSubmitAudit = () => {
  console.log('提交审核');
  emits('submit-audit', {
    unit: selectedUnit.value,
    year: selectedYear.value,
    data: tableData.value,
  });
};

const handleClose = () => {
  showModal.value = false;
};
</script>

<style scoped>
.plan-form-modal {
  padding: 0;
}

.filter-section {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 40px;
}

.notice-section {
  margin-bottom: 16px;
}

.table-section {
  margin-bottom: 20px;
  max-height: 400px;
  overflow: auto;
}

.footer-buttons {
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    padding: 12px;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .filter-label {
    min-width: auto;
  }
}
</style>
