import type { RouteRecordRaw } from 'vue-router'

// 数据分析模块路由配置
export const dataAnalysisRoutes: RouteRecordRaw[] = [
  {
    path: '/data-analysis',
    name: 'DataAnalysis',
    meta: {
      title: '数据统计分析',
      icon: 'Bar<PERSON>hart',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/data-analysis/overview'
      },
      {
        path: 'overview',
        name: 'DataAnalysisOverview',
        component: () => import('@/components/DataAnalysisManagement.vue'),
        meta: {
          title: '数据分析总览',
          icon: 'Dashboard'
        }
      },
      {
        path: 'enhanced',
        name: 'EnhancedDataAnalysis',
        component: () => import('@/components/EnhancedDataAnalysis.vue'),
        meta: {
          title: '增强版数据分析',
          icon: 'TrendingUp'
        }
      },
      {
        path: 'simple',
        name: 'SimpleDataAnalysis',
        component: () => import('@/components/SimpleDataAnalysis.vue'),
        meta: {
          title: '简化版数据分析',
          icon: 'BarChart'
        }
      },
      {
        path: 'naive',
        name: 'NaiveDataAnalysis',
        component: () => import('@/components/NaiveDataAnalysis.vue'),
        meta: {
          title: 'Naive UI版数据分析',
          icon: 'Palette'
        }
      },
      {
        path: 'optimized',
        name: 'OptimizedDataAnalysis',
        component: () => import('@/components/OptimizedDataAnalysis.vue'),
        meta: {
          title: '优化版数据分析',
          icon: 'Zap'
        }
      }
    ]
  }
]

// 导出默认路由配置
export default dataAnalysisRoutes
