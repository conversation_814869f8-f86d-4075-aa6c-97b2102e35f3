<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="新增月计划"
    style="width: 95%; max-width: 1400px"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="month-plan-form-modal">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <n-space align="center" :size="16">
          <div class="filter-item">
            <span class="filter-label">单位:</span>
            <n-select
              v-model:value="selectedUnit"
              :options="unitOptions"
              placeholder="川中油气矿"
              style="width: 150px"
              size="small"
            />
          </div>

          <div class="filter-item">
            <span class="filter-label">月份:</span>
            <n-date-picker
              v-model:value="selectedMonth"
              type="month"
              placeholder="2025年6月"
              style="width: 150px"
              size="small"
            />
          </div>
        </n-space>
      </div>
      <!-- 数据表格 -->
      <div class="table-section">
        <n-data-table
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
          :bordered="true"
          :single-line="false"
          size="small"
          striped
          :scroll-x="1200"
        />
      </div>

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <n-space justify="center" :size="16">
          <n-button type="primary" @click="handleSave"> 保存 </n-button>
          <n-button type="info" @click="handleSubmitAudit"> 提交审核 </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, h } from 'vue';
import {
  NModal,
  NSpace,
  NDatePicker,
  NSelect,
  NButton,
  NDataTable,
  NAlert,
  NInput,
  NTag,
  type DataTableColumns,
} from 'naive-ui';

// Props
const props = defineProps<{
  show: boolean;
}>();

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean];
  save: [data: any];
  'submit-audit': [data: any];
}>();

// 响应式数据
const showModal = ref(props.show);
const selectedUnit = ref('川中油气矿');
const selectedMonth = ref(new Date().getTime());

// 监听props变化
watch(
  () => props.show,
  newVal => {
    showModal.value = newVal;
  }
);

// 监听modal变化
watch(showModal, newVal => {
  emits('update:show', newVal);
});

// 下拉选项数据
const unitOptions = [
  { label: '川中油气矿', value: '川中油气矿' },
  { label: '重庆气矿', value: '重庆气矿' },
  { label: '川西北气矿', value: '川西北气矿' },
  { label: '川东北气矿', value: '川东北气矿' },
];

const domainOptions = [
  { label: '钻井', value: '钻井' },
  { label: '完井', value: '完井' },
  { label: '采油', value: '采油' },
  { label: '修井', value: '修井' },
  { label: '测试', value: '测试' },
  { label: '压裂', value: '压裂' },
];

const wellNumberOptions = [
  { label: '威203-H1', value: '威203-H1' },
  { label: '威201-H2', value: '威201-H2' },
  { label: '威039-H1', value: '威039-H1' },
  { label: '威039-H2', value: '威039-H2' },
  { label: '威039-H3', value: '威039-H3' },
  { label: '威039-H4', value: '威039-H4' },
  { label: '威039-H5', value: '威039-H5' },
  { label: '威039-H6', value: '威039-H6' },
  { label: '威威129H', value: '威威129H' },
];

const measureTypeOptions = [
  { label: '本月度未实施', value: '本月度未实施' },
  { label: '新增', value: '新增' },
  { label: '奇偶', value: '奇偶' },
];

const implementTypeOptions = [
  { label: '本月度未实施', value: '本月度未实施' },
  { label: '新增', value: '新增' },
  { label: '奇偶', value: '奇偶' },
];

// 获取状态标签样式
const getStatusStyle = (status: string) => {
  switch (status) {
    case '本月度未实施':
      return { type: 'error' as const, color: '#ff4d4f' };
    case '新增':
      return { type: 'success' as const, color: '#52c41a' };
    case '奇偶':
      return { type: 'warning' as const, color: '#faad14' };
    default:
      return { type: 'default' as const, color: '#d9d9d9' };
  }
};

// 创建可编辑单元格的渲染函数
const createEditableCell = (
  key: string,
  options?: Array<{ label: string; value: string }>,
  type: 'input' | 'select' | 'date' = 'input'
) => {
  return (row: any, index: number) => {
    const value = row[key] || '';

    if (type === 'select' && options) {
      return h(NSelect, {
        value: value,
        options: options,
        placeholder: '请选择',
        size: 'small',
        style: { width: '100%' },
        onUpdateValue: (newValue: string) => {
          (tableData.value[index] as any)[key] = newValue;
        },
      });
    } else if (type === 'date') {
      return h(NDatePicker, {
        value: value ? new Date(value).getTime() : null,
        type: 'date',
        placeholder: '选择日期',
        size: 'small',
        style: { width: '100%' },
        onUpdateValue: (newValue: number | null) => {
          (tableData.value[index] as any)[key] = newValue
            ? new Date(newValue).toISOString().split('T')[0]
            : '';
        },
      });
    } else {
      return h(NInput, {
        value: value,
        placeholder: '请输入',
        size: 'small',
        onUpdateValue: (newValue: string) => {
          (tableData.value[index] as any)[key] = newValue;
        },
      });
    }
  };
};

// 创建带标签的可编辑单元格
const createTagEditableCell = (
  key: string,
  options: Array<{ label: string; value: string }>
) => {
  return (row: any, index: number) => {
    const value = row[key] || '';

    if (value) {
      const style = getStatusStyle(value);
      return h(
        'div',
        {
          style: { position: 'relative' },
        },
        [
          h(NSelect, {
            value: value,
            options: options,
            placeholder: '请选择',
            size: 'small',
            style: { width: '100%' },
            onUpdateValue: (newValue: string) => {
              (tableData.value[index] as any)[key] = newValue;
            },
          }),
          value &&
            h(
              NTag,
              {
                type: style.type,
                size: 'small',
                style: {
                  position: 'absolute',
                  top: '2px',
                  right: '2px',
                  backgroundColor: style.color,
                  color: '#fff',
                  border: 'none',
                  fontSize: '10px',
                  padding: '0 4px',
                  zIndex: 10,
                },
              },
              { default: () => value }
            ),
        ]
      );
    }

    return h(NSelect, {
      value: value,
      options: options,
      placeholder: '请选择',
      size: 'small',
      style: { width: '100%' },
      onUpdateValue: (newValue: string) => {
        (tableData.value[index] as any)[key] = newValue;
      },
    });
  };
};

// 表格列定义
const columns: DataTableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (_, index) => index + 1,
    fixed: 'left',
  },
  {
    title: '单位',
    key: 'unit',
    width: 120,
    render: (row: any, index: number) =>
      createEditableCell('unit', unitOptions, 'select')(row, index),
  },
  {
    title: '领域',
    key: 'domain',
    width: 100,
    render: (row: any, index: number) =>
      createEditableCell('domain', domainOptions, 'select')(row, index),
  },
  {
    title: '井号',
    key: 'wellNumber',
    width: 120,
    render: (row: any, index: number) =>
      createEditableCell('wellNumber', wellNumberOptions, 'select')(row, index),
  },
  {
    title: '年度计划安排',
    key: 'yearPlan',
    width: 120,
    render: (row: any, index: number) =>
      createEditableCell('yearPlan', undefined, 'input')(row, index),
  },
  {
    title: '预计开口时间',
    key: 'expectedStartTime',
    width: 140,
    render: (row: any, index: number) =>
      createEditableCell('expectedStartTime', undefined, 'date')(row, index),
  },
  {
    title: '措施类型',
    key: 'measureType',
    width: 120,
    render: (row: any, index: number) =>
      createTagEditableCell('measureType', measureTypeOptions)(row, index),
  },
  {
    title: '安排类型',
    key: 'implementType',
    width: 120,
    render: (row: any, index: number) =>
      createTagEditableCell('implementType', implementTypeOptions)(row, index),
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (_, index: number) => {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          onClick: () => handleDeleteRow(index),
        },
        { default: () => '删除' }
      );
    },
  },
];

// 模拟表格数据
const tableData = ref([
  {
    unit: '',
    domain: '',
    wellNumber: '威203-H1',
    yearPlan: '2025-5',
    expectedStartTime: '2025-5-20',
    measureType: '',
    implementType: '',
    highlighted: false,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威201-H2',
    yearPlan: '2025-5',
    expectedStartTime: '',
    measureType: '本月度未实施',
    implementType: '',
    highlighted: false,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H1',
    yearPlan: '2025-5',
    expectedStartTime: '',
    measureType: '本月度未实施',
    implementType: '',
    highlighted: false,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H2',
    yearPlan: '2025-4',
    expectedStartTime: '2025-5-11',
    measureType: '',
    implementType: '',
    highlighted: true,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H3',
    yearPlan: '',
    expectedStartTime: '2025-5-9',
    measureType: '新增',
    implementType: '',
    highlighted: true,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H4',
    yearPlan: '',
    expectedStartTime: '2025-5-18',
    measureType: '新增',
    implementType: '',
    highlighted: true,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H5',
    yearPlan: '',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
    highlighted: false,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威039-H6',
    yearPlan: '',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
    highlighted: false,
  },
  {
    unit: '',
    domain: '',
    wellNumber: '威威129H',
    yearPlan: '',
    expectedStartTime: '',
    measureType: '',
    implementType: '',
    highlighted: false,
  },
]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount || 0} 条`,
});

// 方法
const handleSave = () => {
  console.log('保存数据');
  emits('save', {
    unit: selectedUnit.value,
    month: selectedMonth.value,
    data: tableData.value,
  });
};

const handleSubmitAudit = () => {
  console.log('提交审核');
  emits('submit-audit', {
    unit: selectedUnit.value,
    month: selectedMonth.value,
    data: tableData.value,
  });
};

const handleClose = () => {
  showModal.value = false;
};

const handleDeleteRow = (index: number) => {
  if (tableData.value.length > 1) {
    tableData.value.splice(index, 1);
  }
};
</script>

<style scoped>
.month-plan-form-modal {
  padding: 0;
}

.filter-section {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 40px;
}

.notice-section {
  margin-bottom: 16px;
}

.notice-content {
  line-height: 1.6;
}

.notice-item {
  margin-bottom: 4px;
  font-size: 13px;
}

.table-section {
  margin-bottom: 20px;
  max-height: 500px;
  overflow: auto;
}

.footer-buttons {
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px;
}

/* 高亮行样式 */
:deep(.n-data-table-tr--highlighted) {
  background-color: #fff3cd !important;
}

/* 自定义标签样式 */
:deep(.n-tag) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    padding: 12px;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .filter-label {
    min-width: auto;
  }

  .notice-item {
    font-size: 12px;
  }
}
</style>
