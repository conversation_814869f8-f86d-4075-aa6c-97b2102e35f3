/*
 * @Description:
 * @Author: wangfuquan
 * @Date: 2025-05-26 17:27:38
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-06-09 13:51:23
 * @FilePath: \aiTest\vite.config.ts
 */
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { fileURLToPath, URL } from "node:url";

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
});
