import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

// ECharts 主题配置
export const echartsTheme = {
  color: [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', 
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6'
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 12,
    color: '#333'
  },
  title: {
    textStyle: {
      color: '#333',
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  legend: {
    textStyle: {
      color: '#666',
      fontSize: 12
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true
  }
}

// 注册主题
echarts.registerTheme('custom', echartsTheme)

// 通用图表配置
export const commonChartOptions: Partial<EChartsOption> = {
  animation: true,
  animationDuration: 1000,
  animationEasing: 'cubicOut',
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#ddd',
    borderWidth: 1,
    textStyle: {
      color: '#333',
      fontSize: 12
    },
    padding: [8, 12],
    extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;'
  }
}

// 创建图表实例
export const createChart = (container: HTMLElement, theme: string = 'custom'): ECharts => {
  return echarts.init(container, theme, {
    renderer: 'canvas',
    useDirtyRect: false
  })
}

// 销毁图表实例
export const disposeChart = (chart: ECharts | null): void => {
  if (chart && !chart.isDisposed()) {
    chart.dispose()
  }
}

// 响应式处理
export const handleChartResize = (chart: ECharts | null): void => {
  if (chart && !chart.isDisposed()) {
    chart.resize()
  }
}

// 柱状图配置生成器
export const createBarChartOption = (data: {
  categories: string[]
  series: Array<{
    name: string
    data: number[]
    color?: string
  }>
  title?: string
  yAxisName?: string
}): EChartsOption => {
  return {
    ...commonChartOptions,
    title: data.title ? {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    } : undefined,
    legend: {
      data: data.series.map(s => s.name),
      top: data.title ? 40 : 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: {
      type: 'category',
      data: data.categories,
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      }
    },
    yAxis: {
      type: 'value',
      name: data.yAxisName || '',
      nameTextStyle: {
        fontSize: 12,
        color: '#666'
      },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      },
      splitLine: {
        lineStyle: { color: '#f0f0f0' }
      }
    },
    series: data.series.map(s => ({
      name: s.name,
      type: 'bar',
      data: s.data,
      itemStyle: {
        color: s.color || undefined,
        borderRadius: [2, 2, 0, 0]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }))
  }
}

// 折线图配置生成器
export const createLineChartOption = (data: {
  categories: string[]
  series: Array<{
    name: string
    data: number[]
    color?: string
    lineStyle?: 'solid' | 'dashed' | 'dotted'
    symbol?: string
  }>
  title?: string
  yAxisName?: string
}): EChartsOption => {
  return {
    ...commonChartOptions,
    title: data.title ? {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    } : undefined,
    legend: {
      data: data.series.map(s => s.name),
      top: data.title ? 40 : 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.categories,
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      }
    },
    yAxis: {
      type: 'value',
      name: data.yAxisName || '',
      nameTextStyle: {
        fontSize: 12,
        color: '#666'
      },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      },
      splitLine: {
        lineStyle: { color: '#f0f0f0' }
      }
    },
    series: data.series.map(s => ({
      name: s.name,
      type: 'line',
      data: s.data,
      itemStyle: {
        color: s.color || undefined
      },
      lineStyle: {
        type: s.lineStyle || 'solid',
        width: 2
      },
      symbol: s.symbol || 'circle',
      symbolSize: 4,
      emphasis: {
        focus: 'series',
        lineStyle: { width: 3 },
        symbolSize: 6
      }
    }))
  }
}

// 混合图表配置生成器（柱状图+折线图）
export const createMixedChartOption = (data: {
  categories: string[]
  barSeries: Array<{
    name: string
    data: number[]
    color?: string
  }>
  lineSeries: Array<{
    name: string
    data: number[]
    color?: string
    yAxisIndex?: number
  }>
  title?: string
  yAxis?: Array<{
    name: string
    position: 'left' | 'right'
  }>
}): EChartsOption => {
  const allSeries = [...data.barSeries.map(s => s.name), ...data.lineSeries.map(s => s.name)]
  
  return {
    ...commonChartOptions,
    title: data.title ? {
      text: data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    } : undefined,
    legend: {
      data: allSeries,
      top: data.title ? 40 : 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: {
      type: 'category',
      data: data.categories,
      axisPointer: { type: 'shadow' },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      }
    },
    yAxis: data.yAxis ? data.yAxis.map((axis, index) => ({
      type: 'value',
      name: axis.name,
      position: axis.position,
      nameTextStyle: {
        fontSize: 12,
        color: '#666'
      },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      },
      splitLine: index === 0 ? {
        lineStyle: { color: '#f0f0f0' }
      } : { show: false }
    })) : [{
      type: 'value',
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      },
      splitLine: {
        lineStyle: { color: '#f0f0f0' }
      }
    }],
    series: [
      ...data.barSeries.map(s => ({
        name: s.name,
        type: 'bar' as const,
        data: s.data,
        itemStyle: {
          color: s.color || undefined,
          borderRadius: [2, 2, 0, 0]
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      })),
      ...data.lineSeries.map(s => ({
        name: s.name,
        type: 'line' as const,
        yAxisIndex: s.yAxisIndex || 0,
        data: s.data,
        itemStyle: {
          color: s.color || undefined
        },
        lineStyle: { width: 3 },
        symbol: 'circle',
        symbolSize: 6,
        emphasis: {
          focus: 'series',
          lineStyle: { width: 4 },
          symbolSize: 8
        }
      }))
    ]
  }
}

// 导出图表为图片
export const exportChartAsImage = (
  chart: ECharts,
  filename: string = 'chart',
  type: 'png' | 'jpeg' = 'png'
): void => {
  const url = chart.getDataURL({
    type,
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
  
  const link = document.createElement('a')
  link.download = `${filename}.${type}`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 默认导出
export default echarts
