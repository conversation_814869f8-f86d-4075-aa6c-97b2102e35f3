import * as XLSX from 'xlsx'
import type { StatisticsData, MonthlyOutputData, YearlyComparisonData } from '@/types/dataAnalysis'

// 格式化数字显示
export const formatNumber = (value: number | string, decimals: number = 2): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0'
  return num.toFixed(decimals)
}

// 计算完成率
export const calculateCompletionRate = (actual: number, plan: number): string => {
  if (plan === 0) return '0.00'
  return ((actual / plan) * 100).toFixed(2)
}

// 计算超欠量
export const calculateExcess = (actual: number, plan: number): string => {
  const excess = actual - plan
  return excess >= 0 ? `+${formatNumber(excess)}` : formatNumber(excess)
}

// 生成图表颜色
export const getChartColors = () => {
  return [
    '#5470c6', '#91cc75', '#fac858', '#ee6666',
    '#73c0de', '#3ba272', '#fc8452', '#9a60b4',
    '#ea7ccc', '#5470c6', '#91cc75', '#fac858'
  ]
}

// 导出Excel
export const exportToExcel = (data: any[], filename: string) => {
  const ws = XLSX.utils.json_to_sheet(data)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
  XLSX.writeFile(wb, `${filename}.xlsx`)
}

// 格式化月度数据用于导出
export const formatMonthlyDataForExport = (data: MonthlyOutputData[]) => {
  return data.map(item => ({
    '月份': item.month,
    '计划产量(亿方)': formatNumber(item.planOutput),
    '实际产量(亿方)': formatNumber(item.actualOutput),
    '累计计划产量(亿方)': formatNumber(item.cumulativePlan),
    '累计实际产量(亿方)': formatNumber(item.cumulativeActual),
    '完成率(%)': calculateCompletionRate(item.actualOutput, item.planOutput),
    '超欠(亿方)': calculateExcess(item.actualOutput, item.planOutput)
  }))
}

// 格式化年度对比数据用于导出
export const formatYearlyDataForExport = (data: YearlyComparisonData[]) => {
  return data.map(item => ({
    '月份': item.month,
    '当年新井计划产量(亿方)': formatNumber(item.currentWellPlan),
    '当年新井实际产量(亿方)': formatNumber(item.currentWellActual),
    '上半年新井计划产量(亿方)': formatNumber(item.firstHalfWellPlan),
    '上半年新井实际产量(亿方)': formatNumber(item.firstHalfWellActual),
    '旧井计划产量(亿方)': formatNumber(item.oldWellPlan),
    '旧井实际产量(亿方)': formatNumber(item.oldWellActual)
  }))
}

// 格式化统计数据用于导出
export const formatStatisticsForExport = (data: StatisticsData) => {
  return [
    {
      '指标': '计划产量',
      '数值': `${data.planOutput}亿方`,
      '备注': '年度总计划产量'
    },
    {
      '指标': '实际产量',
      '数值': `${data.actualOutput}亿方`,
      '备注': '年度总实际产量'
    },
    {
      '指标': '产量完成率',
      '数值': `${data.completionRate}%`,
      '备注': '实际产量/计划产量'
    },
    {
      '指标': '旧井产量',
      '数值': `${data.oldWell.value}亿方`,
      '备注': `实际产量${data.oldWell.actual}%，超欠${data.oldWell.excess}亿方`
    },
    {
      '指标': '上半年新井产量',
      '数值': `${data.newWell.value}亿方`,
      '备注': `实际产量${data.newWell.actual}%，超欠${data.newWell.excess}亿方`
    },
    {
      '指标': '当年新井产量',
      '数值': `${data.currentWell.value}亿方`,
      '备注': `实际产量${data.currentWell.actual}%，超欠${data.currentWell.excess}亿方`
    }
  ]
}

// 生成模拟数据
export const generateMockData = () => {
  const months = ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
                  '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12']
  
  const monthlyData: MonthlyOutputData[] = months.map((month, index) => {
    const planOutput = 30 + Math.random() * 10
    const actualOutput = planOutput * (0.8 + Math.random() * 0.4)
    const cumulativePlan = (index + 1) * 35
    const cumulativeActual = (index + 1) * 32
    
    return {
      month,
      planOutput: Number(planOutput.toFixed(2)),
      actualOutput: Number(actualOutput.toFixed(2)),
      cumulativePlan: Number(cumulativePlan.toFixed(2)),
      cumulativeActual: Number(cumulativeActual.toFixed(2))
    }
  })
  
  const yearlyData: YearlyComparisonData[] = months.map(month => ({
    month,
    currentWellPlan: Number((8 - Math.random() * 2).toFixed(2)),
    currentWellActual: Number((7 - Math.random() * 2).toFixed(2)),
    firstHalfWellPlan: Number((6 - Math.random() * 1).toFixed(2)),
    firstHalfWellActual: Number((5.5 - Math.random() * 1).toFixed(2)),
    oldWellPlan: Number((20 + Math.random() * 10).toFixed(2)),
    oldWellActual: Number((18 + Math.random() * 8).toFixed(2))
  }))
  
  return { monthlyData, yearlyData }
}

// 日期格式化
export const formatDate = (date: Date | number, format: string = 'YYYY-MM-DD'): string => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
}

// 获取日期范围
export const getDateRange = (type: 'year' | 'month' | 'quarter'): [number, number] => {
  const now = new Date()
  const year = now.getFullYear()
  
  switch (type) {
    case 'year':
      return [
        new Date(year, 0, 1).getTime(),
        new Date(year, 11, 31).getTime()
      ]
    case 'month':
      return [
        new Date(year, now.getMonth(), 1).getTime(),
        new Date(year, now.getMonth() + 1, 0).getTime()
      ]
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3)
      return [
        new Date(year, quarter * 3, 1).getTime(),
        new Date(year, quarter * 3 + 3, 0).getTime()
      ]
    default:
      return [now.getTime(), now.getTime()]
  }
}
