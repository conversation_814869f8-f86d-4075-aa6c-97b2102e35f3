import { TSpanProps } from './../node_modules/zrender/src/graphic/TSpan';
/*
 * @Description: 
 * @Author: wangfuquan
 * @Date: 2025-05-26 17:28:07
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-06-09 11:14:02
 * @FilePath: \aiTest\src\main.ts
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import YearPlanManagement from './components/YearPlanManagement.vue'
import MonthPlanManagement from './components/MonthPlanManagement.vue'
import MeasureTrackingManagement from './components/MeasureTrackingManagement.vue'
import dataAnalysisRoutes from './router/dataAnalysis'
// 创建路由
const routes = [
  ...dataAnalysisRoutes,
  { path: '/', redirect: '/year-plan' },
  { path: '/year-plan', component: YearPlanManagement },
  { path: '/month-plan', component: MonthPlanManagement },
  { path: '/measure-tracking', component: MeasureTrackingManagement },
  { path: '/year-statistics', component: YearPlanManagement } // 暂时使用同一个组件
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.mount('#app')
