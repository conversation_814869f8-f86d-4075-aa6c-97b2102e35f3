import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import YearPlanManagement from './components/YearPlanManagement.vue'
import MonthPlanManagement from './components/MonthPlanManagement.vue'

// 创建路由
const routes = [
  { path: '/', redirect: '/year-plan' },
  { path: '/year-plan', component: YearPlanManagement },
  { path: '/month-plan', component: MonthPlanManagement },
  { path: '/year-statistics', component: YearPlanManagement } // 暂时使用同一个组件
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.mount('#app')
