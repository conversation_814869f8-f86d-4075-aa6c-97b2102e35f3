<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1714890910933" clover="3.2.0">
  <project timestamp="1714890910934" name="All files">
    <metrics statements="20" coveredstatements="20" conditionals="8" coveredconditionals="4" methods="6" coveredmethods="6" elements="34" coveredelements="30" complexity="0" loc="20" ncloc="20" packages="1" files="1" classes="1"/>
    <file name="index.ts" path="/Users/<USER>/dev/css-render/packages/vue3-ssr/src/index.ts">
      <metrics statements="20" coveredstatements="20" conditionals="8" coveredconditionals="4" methods="6" coveredmethods="6"/>
      <line num="9" count="1" type="stmt"/>
      <line num="12" count="2" type="stmt"/>
      <line num="20" count="2" type="stmt"/>
      <line num="22" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="23" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="24" count="2" type="stmt"/>
      <line num="25" count="2" type="stmt"/>
      <line num="29" count="1" type="stmt"/>
      <line num="37" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="38" count="2" type="stmt"/>
      <line num="39" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="40" count="2" type="stmt"/>
      <line num="41" count="2" type="stmt"/>
      <line num="51" count="2" type="stmt"/>
      <line num="52" count="2" type="stmt"/>
      <line num="56" count="2" type="stmt"/>
      <line num="57" count="2" type="stmt"/>
      <line num="59" count="2" type="stmt"/>
      <line num="60" count="2" type="stmt"/>
      <line num="61" count="2" type="stmt"/>
    </file>
  </project>
</coverage>
