<!--
 * @Description: 
 * @Author: wangfuquan
 * @Date: 2025-05-26 16:24:40
 * @LastEditors: wangfuquan
 * @LastEditTime: 2025-05-28 14:17:44
 * @FilePath: \kfsc-web\packages\daily-report\src\views\measure-monthly-plan\index.vue
-->
<template>
  <div class="month-plan-container">
    <!-- 顶部导航 -->
    <div class="header-nav">
      <n-tabs v-model:value="activeTab" type="line" size="large">
        <n-tab-pane name="management" tab="月计划管理" />
        <n-tab-pane name="statistics" tab="月计划统计" />
      </n-tabs>
    </div>

    <!-- 根据activeTab显示不同的内容 -->
    <div class="tab-content">
      <MonthPlanManagementTab v-if="activeTab === 'management'" />
      <MonthPlanStatisticsTab v-else-if="activeTab === 'statistics'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NTabs, NTabPane } from 'naive-ui'
import MonthPlanManagementTab from './MonthPlanManagementTab.vue'
import MonthPlanStatisticsTab from './MonthPlanStatisticsTab.vue'

// 响应式数据
const activeTab = ref('management')
</script>

<style scoped>
.month-plan-container {
  padding: 20px;
  background-color: #f5f5f5;
  background-color: #fff;
  min-height: 100vh;
}

.header-nav {
  /* margin-bottom: 16px; */
  background-color: #fff;
  padding: 0 16px;
  border-radius: 6px;
}

.tab-content {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .month-plan-container {
    padding: 10px;
  }
}
</style>
