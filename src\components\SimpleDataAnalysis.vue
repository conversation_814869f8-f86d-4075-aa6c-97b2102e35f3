<template>
  <div class="simple-data-analysis">
    <!-- 顶部标签页 -->
    <n-tabs v-model:value="activeTab" type="line" size="large">
      <n-tab-pane name="year" tab="年度产量运行分析" />
      <n-tab-pane name="month" tab="月度产量运行分析" />
    </n-tabs>

    <!-- 筛选区域 -->
    <n-card :bordered="false" class="filter-card">
      <n-space align="center" :size="16" wrap>
        <n-space align="center" :size="8">
          <span class="filter-label">共类型:</span>
          <n-select
            v-model:value="filterData.type"
            :options="typeOptions"
            placeholder="请选择类型"
            style="width: 150px"
            clearable
          />
        </n-space>

        <n-space align="center" :size="8">
          <span class="filter-label">日期选择:</span>
          <n-date-picker
            v-model:value="filterData.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 280px"
            clearable
          />
        </n-space>

        <n-space :size="8">
          <n-button type="primary" @click="handleQuery" :loading="loading">
            查询
          </n-button>
          <n-button type="success" @click="handleExport">
            导出
          </n-button>
        </n-space>
      </n-space>
    </n-card>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 左侧统计卡片 -->
      <div class="left-stats">
        <!-- 主要指标卡片 -->
        <n-card class="stat-card plan-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#52c41a">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.planOutput)" :precision="2">
                <template #suffix>亿方</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">计划产量</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <n-card class="stat-card actual-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#fa8c16">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.actualOutput)" :precision="2">
                <template #suffix>亿方</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">实际产量</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <n-card class="stat-card completion-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#f5222d">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12H12V6Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.completionRate)" :precision="2">
                <template #suffix>%</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">产量完成率</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <!-- 详细统计卡片 -->
        <div class="detail-stats">
          <n-card class="detail-card old-well-card" :bordered="false">
            <template #header>
              <n-space align="center" :size="8">
                <n-tag type="success" size="small">旧井</n-tag>
                <span style="font-weight: bold; font-size: 18px;">{{ detailStats.oldWell.value }}</span>
                <span style="color: #666; font-size: 12px;">亿方</span>
              </n-space>
            </template>
            <n-space vertical :size="8">
              <n-space justify="space-between">
                <span style="color: #666;">生产时率</span>
                <n-tag type="info" size="small">{{ detailStats.oldWell.rate }}%</n-tag>
              </n-space>
              <n-space justify="space-between">
                <span style="color: #666;">超欠</span>
                <n-tag type="success" size="small">{{ detailStats.oldWell.excess }}亿方</n-tag>
              </n-space>
            </n-space>
          </n-card>

          <n-card class="detail-card new-well-card" :bordered="false">
            <template #header>
              <n-space align="center" :size="8">
                <n-tag type="info" size="small">上半年新井</n-tag>
                <span style="font-weight: bold; font-size: 18px;">{{ detailStats.newWell.value }}</span>
                <span style="color: #666; font-size: 12px;">亿方</span>
              </n-space>
            </template>
            <n-space vertical :size="8">
              <n-space justify="space-between">
                <span style="color: #666;">生产时率</span>
                <n-tag type="info" size="small">{{ detailStats.newWell.rate }}%</n-tag>
              </n-space>
              <n-space justify="space-between">
                <span style="color: #666;">超欠</span>
                <n-tag type="success" size="small">{{ detailStats.newWell.excess }}亿方</n-tag>
              </n-space>
            </n-space>
          </n-card>

          <n-card class="detail-card current-well-card" :bordered="false">
            <template #header>
              <n-space align="center" :size="8">
                <n-tag type="warning" size="small">当年新井</n-tag>
                <span style="font-weight: bold; font-size: 18px;">{{ detailStats.currentWell.value }}</span>
                <span style="color: #666; font-size: 12px;">亿方</span>
              </n-space>
            </template>
            <n-space vertical :size="8">
              <n-space justify="space-between">
                <span style="color: #666;">生产时率</span>
                <n-tag type="info" size="small">{{ detailStats.currentWell.rate }}%</n-tag>
              </n-space>
              <n-space justify="space-between">
                <span style="color: #666;">超欠</span>
                <n-tag type="error" size="small">{{ detailStats.currentWell.excess }}亿方</n-tag>
              </n-space>
            </n-space>
          </n-card>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="right-charts">
        <!-- 第一个图表 -->
        <n-card class="chart-section" :bordered="false">
          <template #header>
            <n-space align="center" :size="8">
              <n-icon size="16" color="#1890ff">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                </svg>
              </n-icon>
              <span style="font-weight: 500;">2025年计划实际月度产量及累计产量对比图</span>
            </n-space>
          </template>
          <div class="chart-container">
            <div ref="monthlyChartRef" class="chart-content"></div>
          </div>
        </n-card>

        <!-- 第二个图表 -->
        <n-card class="chart-section" :bordered="false">
          <template #header>
            <n-space align="center" :size="8">
              <n-icon size="16" color="#1890ff">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2Z"/>
                </svg>
              </n-icon>
              <span style="font-weight: 500;">2025年月度与年度计划对比曲线图</span>
            </n-space>
          </template>
          <div class="chart-container">
            <div ref="trendChartRef" class="chart-content"></div>
          </div>
        </n-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  NTabs,
  NTabPane,
  NCard,
  NSpace,
  NSelect,
  NDatePicker,
  NButton,
  NAvatar,
  NIcon,
  NStatistic,
  NTag
} from 'naive-ui'
import * as echarts from 'echarts'

// 响应式数据
const activeTab = ref('year')
const monthlyChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
const loading = ref(false)

// 筛选数据
const filterData = reactive({
  type: '',
  dateRange: null as [number, number] | null
})

// 选项数据
const typeOptions = [
  { label: '全部类型', value: '' },
  { label: '计划产量', value: 'plan' },
  { label: '实际产量', value: 'actual' },
  { label: '累计产量', value: 'cumulative' }
]

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailStats = reactive({
  oldWell: {
    value: '73.89',
    rate: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    rate: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    rate: '98.87',
    excess: '24.31'
  }
})

// 方法
const handleQuery = async () => {
  loading.value = true
  try {
    console.log('查询数据', filterData)
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟API调用
    initCharts()
  } finally {
    loading.value = false
  }
}

const handleExport = () => {
  console.log('导出数据')
  // 这里可以实现数据导出逻辑
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initMonthlyChart()
  initTrendChart()
}

// 初始化月度产量对比图表
const initMonthlyChart = () => {
  if (!monthlyChartRef.value) return
  
  const chart = echarts.init(monthlyChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: [{
      type: 'category',
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisPointer: { type: 'shadow' }
    }],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: { formatter: '{value}' }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: { formatter: '{value}' }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
        itemStyle: { color: '#fac858' },
        lineStyle: { width: 3 }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
        itemStyle: { color: '#ee6666' },
        lineStyle: { width: 3 }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化趋势对比图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  const chart = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 10,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12']
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)'
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted' }
      }
    ]
  }

  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.simple-data-analysis {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  font-weight: 500;
}

/* 主要内容区域 */
.content-area {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

/* 左侧统计区域 */
.left-stats {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 统计卡片样式 */
.stat-card {
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card:hover {
  transform: translateY(-2px);
}

.plan-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.completion-card {
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

/* 详细统计卡片 */
.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.detail-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
}

.old-well-card {
  border-left: 4px solid #52c41a;
}

.new-well-card {
  border-left: 4px solid #1890ff;
}

.current-well-card {
  border-left: 4px solid #fa8c16;
}

/* 右侧图表区域 */
.right-charts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-section {
  flex: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-container {
  height: calc(50vh - 120px);
  min-height: 250px;
}

.chart-content {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
    height: auto;
  }

  .left-stats {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 1;
    min-width: 200px;
  }

  .detail-stats {
    flex-direction: row;
    gap: 16px;
  }

  .detail-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .simple-data-analysis {
    padding: 12px;
  }

  .filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
  }

  .left-stats {
    flex-direction: column;
  }

  .detail-stats {
    flex-direction: column;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
