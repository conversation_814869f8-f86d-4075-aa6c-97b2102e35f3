<template>
  <div class="simple-data-analysis">
    <!-- 顶部标签页 -->
    <n-tabs v-model:value="activeTab" type="line" size="large">
      <n-tab-pane name="year" tab="年度产量运行分析" />
      <n-tab-pane name="month" tab="月度产量运行分析" />
    </n-tabs>

    <!-- 筛选区域 -->
    <n-card :bordered="false" class="filter-card">
      <n-space align="center" :size="16">
        <n-space align="center" :size="8">
          <span class="filter-label">共类型:</span>
          <n-select
            v-model:value="filterData.type"
            :options="typeOptions"
            placeholder="请选择类型"
            style="width: 150px"
            clearable
          />
        </n-space>

        <n-space align="center" :size="8">
          <span class="filter-label">日期选择:</span>
          <n-date-picker
            v-model:value="filterData.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 280px"
            clearable
          />
        </n-space>

        <n-space :size="8">
          <n-button type="primary" @click="handleQuery" :loading="loading">
            查询
          </n-button>
          <n-button type="success" @click="handleExport">
            导出
          </n-button>
        </n-space>
      </n-space>
    </n-card>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 左侧统计卡片 -->
      <div class="left-stats">
        <!-- 主要指标卡片 -->
        <n-card class="stat-card plan-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#52c41a">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.planOutput)" :precision="2">
                <template #suffix>亿方</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">计划产量</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <n-card class="stat-card actual-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#fa8c16">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.actualOutput)" :precision="2">
                <template #suffix>亿方</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">实际产量</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <n-card class="stat-card completion-card" :bordered="false">
          <n-space align="center" :size="16">
            <n-avatar size="large" color="#f5222d">
              <n-icon size="24">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12H12V6Z"/>
                </svg>
              </n-icon>
            </n-avatar>
            <div class="card-content">
              <n-statistic :value="parseFloat(statisticsData.completionRate)" :precision="2">
                <template #suffix>%</template>
                <template #label>
                  <span style="color: #666; font-size: 14px;">产量完成率</span>
                </template>
              </n-statistic>
            </div>
          </n-space>
        </n-card>

        <!-- 详细统计卡片 -->
        <div class="detail-stats">
          <div class="detail-card old-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.oldWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">旧井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.oldWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value positive">{{ detailStats.oldWell.excess }}亿方</span>
              </div>
            </div>
          </div>

          <div class="detail-card new-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.newWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">上半年新井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.newWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value positive">{{ detailStats.newWell.excess }}亿方</span>
              </div>
            </div>
          </div>

          <div class="detail-card current-well-card">
            <div class="detail-main">
              <div class="detail-value">{{ detailStats.currentWell.value }}<span class="unit">亿方</span></div>
              <div class="detail-label">当年新井</div>
            </div>
            <div class="detail-info">
              <div class="info-row">
                <span class="info-label">生产时率</span>
                <span class="info-value">{{ detailStats.currentWell.rate }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">超欠</span>
                <span class="info-value negative">{{ detailStats.currentWell.excess }}亿方</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="right-charts">
        <!-- 第一个图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-title">
              <span class="title-icon">🔵</span>
              2025年计划实际月度产量及累计产量对比图
            </div>
          </div>
          <div class="chart-container">
            <div ref="monthlyChartRef" class="chart-content"></div>
          </div>
        </div>

        <!-- 第二个图表 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-title">
              <span class="title-icon">🔵</span>
              2025年月度与年度计划对比曲线图
            </div>
          </div>
          <div class="chart-container">
            <div ref="trendChartRef" class="chart-content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 响应式数据
const activeTab = ref('year')
const monthlyChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 筛选数据
const filterData = reactive({
  type: '',
  startDate: '',
  endDate: ''
})

// 统计数据
const statisticsData = reactive({
  planOutput: '142.01',
  actualOutput: '51.44',
  completionRate: '36.22'
})

// 详细统计数据
const detailStats = reactive({
  oldWell: {
    value: '73.89',
    rate: '91.36',
    excess: '45.42'
  },
  newWell: {
    value: '39.01',
    rate: '98.72',
    excess: '20.83'
  },
  currentWell: {
    value: '29.11',
    rate: '98.87',
    excess: '24.31'
  }
})

// 方法
const handleQuery = () => {
  console.log('查询数据', filterData)
  initCharts()
}

const handleExport = () => {
  console.log('导出数据')
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initMonthlyChart()
  initTrendChart()
}

// 初始化月度产量对比图表
const initMonthlyChart = () => {
  if (!monthlyChartRef.value) return
  
  const chart = echarts.init(monthlyChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['年度计划月度产量', '年度实际月度产量', '年度计划累计产量', '实际累计产量'],
      top: 10,
      textStyle: { fontSize: 12 }
    },
    xAxis: [{
      type: 'category',
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', 
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
      axisPointer: { type: 'shadow' }
    }],
    yAxis: [
      {
        type: 'value',
        name: '月度产量(亿方)',
        position: 'left',
        axisLabel: { formatter: '{value}' }
      },
      {
        type: 'value',
        name: '累计产量(亿方)',
        position: 'right',
        axisLabel: { formatter: '{value}' }
      }
    ],
    series: [
      {
        name: '年度计划月度产量',
        type: 'bar',
        data: [35, 32, 28, 30, 32, 25, 28, 35, 30, 28, 30, 33],
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '年度实际月度产量',
        type: 'bar',
        data: [33, 30, 25, 27, 29, 22, 25, 32, 27, 25, 27, 30],
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '年度计划累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 67, 95, 125, 157, 182, 210, 245, 275, 303, 333, 366],
        itemStyle: { color: '#fac858' },
        lineStyle: { width: 3 }
      },
      {
        name: '实际累计产量',
        type: 'line',
        yAxisIndex: 1,
        data: [33, 63, 88, 115, 144, 166, 191, 223, 250, 275, 302, 332],
        itemStyle: { color: '#ee6666' },
        lineStyle: { width: 3 }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化趋势对比图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  const chart = echarts.init(trendChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['当年新井计划产量', '当年新井实际产量', '上半年新井计划产量', '上半年新井实际产量', '旧井计划产量', '旧井实际产量'],
      top: 10,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06',
             '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12']
    },
    yAxis: {
      type: 'value',
      name: '产量(亿方)'
    },
    series: [
      {
        name: '当年新井计划产量',
        type: 'line',
        data: [8, 7.5, 7, 6.5, 6, 5.5, 5, 4.5, 4, 3.5, 3, 2.5],
        itemStyle: { color: '#5470c6' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '当年新井实际产量',
        type: 'line',
        data: [7.8, 7.2, 6.8, 6.2, 5.8, 5.2, 4.8, 4.2, 3.8, 3.2, 2.8, 2.2],
        itemStyle: { color: '#91cc75' },
        lineStyle: { type: 'solid' }
      },
      {
        name: '上半年新井计划产量',
        type: 'line',
        data: [6, 5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8],
        itemStyle: { color: '#fac858' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '上半年新井实际产量',
        type: 'line',
        data: [5.8, 5.6, 5.4, 5.2, 5, 4.8, 4.6, 4.4, 4.2, 4, 3.8, 3.6],
        itemStyle: { color: '#ee6666' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '旧井计划产量',
        type: 'line',
        data: [21, 19, 15.4, 18, 21, 14.5, 18.2, 25.5, 22, 20.5, 24, 26.7],
        itemStyle: { color: '#73c0de' },
        lineStyle: { type: 'dotted' }
      },
      {
        name: '旧井实际产量',
        type: 'line',
        data: [19.4, 17.4, 12.8, 15.6, 18.2, 12, 15.6, 23.4, 19.2, 17.8, 21.4, 24.2],
        itemStyle: { color: '#3ba272' },
        lineStyle: { type: 'dotted' }
      }
    ]
  }

  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.simple-data-analysis {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 标签页样式 */
.tabs-header {
  display: flex;
  background: white;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  padding: 12px 24px;
  text-align: center;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  border-right: 1px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background: #1890ff;
  color: white;
}

.tab-item:hover:not(.active) {
  background: #e9ecef;
  color: #333;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  font-weight: 500;
}

.filter-select,
.filter-input {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.filter-select:focus,
.filter-input:focus {
  border-color: #1890ff;
}

.filter-select {
  min-width: 120px;
}

.filter-input {
  width: 140px;
}

.date-separator {
  font-size: 14px;
  color: #666;
  margin: 0 4px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.btn {
  padding: 6px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-success {
  background: #52c41a;
  color: white;
}

.btn-success:hover {
  background: #73d13d;
}

/* 主要内容区域 */
.content-area {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

/* 左侧统计区域 */
.left-stats {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 主要统计卡片 */
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.primary-card {
  min-height: 80px;
}

.plan-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.actual-card {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.completion-card {
  background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
}

.icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.icon-circle.green {
  background: #52c41a;
  color: white;
}

.icon-circle.orange {
  background: #fa8c16;
  color: white;
}

.icon-circle.red {
  background: #f5222d;
  color: white;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.card-unit {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 详细统计卡片 */
.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.detail-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #1890ff;
}

.old-well-card {
  border-left-color: #52c41a;
}

.new-well-card {
  border-left-color: #1890ff;
}

.current-well-card {
  border-left-color: #fa8c16;
}

.detail-main {
  margin-bottom: 12px;
}

.detail-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.detail-value .unit {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.detail-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.info-value.positive {
  color: #52c41a;
}

.info-value.negative {
  color: #f5222d;
}

/* 右侧图表区域 */
.right-charts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.title-icon {
  font-size: 12px;
}

.chart-container {
  height: calc(50vh - 120px);
  min-height: 250px;
}

.chart-content {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
    height: auto;
  }

  .left-stats {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-card {
    flex: 1;
    min-width: 200px;
  }

  .detail-stats {
    flex-direction: row;
    gap: 16px;
  }

  .detail-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .simple-data-analysis {
    padding: 12px;
  }

  .filter-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
  }

  .left-stats {
    flex-direction: column;
  }

  .detail-stats {
    flex-direction: column;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
