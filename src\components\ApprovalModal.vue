<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    :title="modalTitle"
    style="width: 500px;"
    :mask-closable="false"
    :closable="true"
    @close="handleClose"
  >
    <div class="approval-modal">
      <!-- 审批选项 -->
      <div class="approval-options">
        <n-radio-group v-model:value="approvalResult" name="approval">
          <n-space direction="vertical" :size="16">
            <n-radio value="agree">
              <span style="color: #1890ff;">同意</span>
            </n-radio>
            <n-radio value="disagree">
              <span style="color: #ff4d4f;">不同意</span>
            </n-radio>
          </n-space>
        </n-radio-group>
      </div>

      <!-- 审批意见输入框 -->
      <div class="opinion-section">
        <n-input
          v-model:value="approvalOpinion"
          type="textarea"
          placeholder="请输入审批意见"
          :rows="4"
          :maxlength="200"
          show-count
          clearable
        />
      </div>

      <!-- 底部按钮 -->
      <div class="footer-buttons">
        <n-space justify="center" :size="16">
          <n-button type="primary" @click="handleConfirm" :loading="loading">
            确认
          </n-button>
          <n-button @click="handleCancel">
            取消
          </n-button>
        </n-space>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import {
  NModal,
  NRadioGroup,
  NRadio,
  NSpace,
  NInput,
  NButton
} from 'naive-ui'

// Props
const props = defineProps<{
  show: boolean
  planName?: string
  planId?: string | number
}>()

// Emits
const emits = defineEmits<{
  'update:show': [value: boolean]
  'confirm': [data: { result: string; opinion: string; planId?: string | number }]
}>()

// 响应式数据
const showModal = ref(props.show)
const approvalResult = ref('agree')
const approvalOpinion = ref('')
const loading = ref(false)

// 计算属性
const modalTitle = computed(() => {
  return props.planName 
    ? `审批【${props.planName}】`
    : '审批'
})

// 监听props变化
watch(() => props.show, (newVal) => {
  showModal.value = newVal
  if (newVal) {
    // 重置表单
    approvalResult.value = 'agree'
    approvalOpinion.value = ''
  }
})

// 监听modal变化
watch(showModal, (newVal) => {
  emits('update:show', newVal)
})

// 方法
const handleConfirm = async () => {
  if (!approvalOpinion.value.trim()) {
    // 这里可以添加提示信息
    console.warn('请输入审批意见')
    return
  }

  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emits('confirm', {
      result: approvalResult.value,
      opinion: approvalOpinion.value.trim(),
      planId: props.planId
    })
    
    showModal.value = false
  } catch (error) {
    console.error('审批提交失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  showModal.value = false
}

const handleClose = () => {
  showModal.value = false
}
</script>

<style scoped>
.approval-modal {
  padding: 0;
}

.approval-options {
  margin-bottom: 20px;
}

.opinion-section {
  margin-bottom: 24px;
}

.footer-buttons {
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

/* 单选按钮样式优化 */
:deep(.n-radio) {
  align-items: center;
}

:deep(.n-radio__label) {
  font-size: 14px;
  font-weight: 500;
}

/* 输入框样式 */
:deep(.n-input) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .approval-modal {
    padding: 0 10px;
  }
}
</style>
